<?php $__env->startSection('title', 'عرض المنتج: ' . $product->name); ?>
<?php $__env->startSection('page-title', 'عرض المنتج'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('products.index')); ?>">المنتجات</a></li>
    <li class="breadcrumb-item active"><?php echo e($product->name); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- Product Information -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">معلومات المنتج</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('products.edit', $product->id)); ?>" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    <a href="<?php echo e(route('products.index')); ?>" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> العودة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <?php if($product->image): ?>
                            <img src="<?php echo e(asset('storage/' . $product->image)); ?>" 
                                 alt="<?php echo e($product->name); ?>" 
                                 class="img-fluid rounded">
                        <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-8">
                        <h4><?php echo e($product->name); ?></h4>
                        <?php if($product->description): ?>
                            <p class="text-muted"><?php echo e($product->description); ?></p>
                        <?php endif; ?>
                        
                        <table class="table table-sm">
                            <tr>
                                <th width="30%">رمز المنتج (SKU):</th>
                                <td><code><?php echo e($product->sku); ?></code></td>
                            </tr>
                            <?php if($product->barcode): ?>
                            <tr>
                                <th>الباركود:</th>
                                <td><code><?php echo e($product->barcode); ?></code></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <th>التصنيف:</th>
                                <td><?php echo e($product->category->name ?? '-'); ?></td>
                            </tr>
                            <tr>
                                <th>المورد:</th>
                                <td><?php echo e($product->supplier->name ?? '-'); ?></td>
                            </tr>
                            <tr>
                                <th>الحالة:</th>
                                <td>
                                    <?php if($product->status == 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Details -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">تفاصيل إضافية</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <?php if($product->weight): ?>
                            <tr>
                                <th>الوزن:</th>
                                <td><?php echo e($product->weight); ?> كجم</td>
                            </tr>
                            <?php endif; ?>
                            <?php if($product->dimensions): ?>
                            <tr>
                                <th>الأبعاد:</th>
                                <td><?php echo e($product->dimensions); ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <th>تاريخ الإنشاء:</th>
                                <td><?php echo e($product->created_at->format('Y-m-d H:i')); ?></td>
                            </tr>
                            <tr>
                                <th>آخر تحديث:</th>
                                <td><?php echo e($product->updated_at->format('Y-m-d H:i')); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <!-- Additional information can be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Pricing Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">معلومات التسعير</h3>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <th>سعر التكلفة:</th>
                        <td class="text-right"><?php echo e(number_format($product->cost_price, 2)); ?> ر.س</td>
                    </tr>
                    <tr>
                        <th>سعر البيع:</th>
                        <td class="text-right"><strong><?php echo e(number_format($product->price, 2)); ?> ر.س</strong></td>
                    </tr>
                    <tr>
                        <th>هامش الربح:</th>
                        <td class="text-right">
                            <?php
                                $profit = $product->price - $product->cost_price;
                                $margin = $product->price > 0 ? ($profit / $product->price) * 100 : 0;
                            ?>
                            <span class="badge badge-<?php echo e($margin > 20 ? 'success' : ($margin > 10 ? 'warning' : 'danger')); ?>">
                                <?php echo e(number_format($margin, 1)); ?>%
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>الربح المتوقع:</th>
                        <td class="text-right"><?php echo e(number_format($profit, 2)); ?> ر.س</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Stock Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">معلومات المخزون</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#stockModal">
                        <i class="fas fa-edit"></i> تحديث
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <th>الكمية الحالية:</th>
                        <td class="text-right">
                            <span class="badge badge-<?php echo e($product->quantity_in_stock <= $product->minimum_stock_level ? 'danger' : 'success'); ?> badge-lg">
                                <?php echo e($product->quantity_in_stock); ?>

                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>الحد الأدنى:</th>
                        <td class="text-right"><?php echo e($product->minimum_stock_level); ?></td>
                    </tr>
                    <tr>
                        <th>حالة المخزون:</th>
                        <td class="text-right">
                            <?php if($product->quantity_in_stock == 0): ?>
                                <span class="badge badge-danger">نفد المخزون</span>
                            <?php elseif($product->quantity_in_stock <= $product->minimum_stock_level): ?>
                                <span class="badge badge-warning">مخزون منخفض</span>
                            <?php else: ?>
                                <span class="badge badge-success">متوفر</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>القيمة الإجمالية:</th>
                        <td class="text-right">
                            <strong><?php echo e(number_format($product->quantity_in_stock * $product->cost_price, 2)); ?> ر.س</strong>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">إجراءات سريعة</h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('products.edit', $product->id)); ?>" class="btn btn-warning btn-block">
                        <i class="fas fa-edit"></i> تعديل المنتج
                    </a>
                    <button type="button" class="btn btn-success btn-block" data-toggle="modal" data-target="#stockModal">
                        <i class="fas fa-boxes"></i> تحديث المخزون
                    </button>
                    <button type="button" class="btn btn-danger btn-block" 
                            onclick="confirmDelete('<?php echo e(route('products.destroy', $product->id)); ?>', 'حذف المنتج', 'هل أنت متأكد من حذف هذا المنتج؟')">
                        <i class="fas fa-trash"></i> حذف المنتج
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stock Update Modal -->
<div class="modal fade" id="stockModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث مخزون: <?php echo e($product->name); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?php echo e(route('products.update-stock', $product->id)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label>المخزون الحالي</label>
                        <input type="text" class="form-control" value="<?php echo e($product->quantity_in_stock); ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label for="type">نوع العملية</label>
                        <select class="form-control" name="type" required>
                            <option value="add">إضافة</option>
                            <option value="subtract">خصم</option>
                            <option value="set">تعيين</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">الكمية</label>
                        <input type="number" class="form-control" name="quantity" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="reason">السبب</label>
                        <input type="text" class="form-control" name="reason" placeholder="سبب التحديث (اختياري)">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    console.log('Product details page loaded');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/products/show.blade.php ENDPATH**/ ?>