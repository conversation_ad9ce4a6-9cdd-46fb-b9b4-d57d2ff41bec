<p class="text-muted mb-3">
    تحديث معلومات الملف الشخصي والبريد الإلكتروني.
</p>

<form id="send-verification" method="post" action="{{ route('verification.send') }}">
    @csrf
</form>

<form method="post" action="{{ route('profile.update') }}">
    @csrf
    @method('patch')

    <div class="form-group">
        <label for="name">الاسم</label>
        <input type="text"
               class="form-control @error('name') is-invalid @enderror"
               id="name"
               name="name"
               value="{{ old('name', $user->name) }}"
               required
               autofocus
               autocomplete="name">
        @error('name')
            <span class="invalid-feedback">{{ $message }}</span>
        @enderror
    </div>

    <div class="form-group">
        <label for="email">البريد الإلكتروني</label>
        <input type="email"
               class="form-control @error('email') is-invalid @enderror"
               id="email"
               name="email"
               value="{{ old('email', $user->email) }}"
               required
               autocomplete="username">
        @error('email')
            <span class="invalid-feedback">{{ $message }}</span>
        @enderror

        @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail())
            <div class="alert alert-warning mt-2">
                <p class="mb-2">
                    <i class="fas fa-exclamation-triangle"></i>
                    بريدك الإلكتروني غير مؤكد.
                </p>
                <button form="send-verification" class="btn btn-sm btn-outline-warning">
                    إرسال رابط التأكيد مرة أخرى
                </button>

                @if (session('status') === 'verification-link-sent')
                    <p class="mt-2 text-success">
                        <i class="fas fa-check"></i>
                        تم إرسال رابط تأكيد جديد إلى بريدك الإلكتروني.
                    </p>
                @endif
            </div>
        @endif
    </div>

    <div class="form-group">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> حفظ
        </button>

        @if (session('status') === 'profile-updated')
            <span class="text-success ml-3">
                <i class="fas fa-check"></i> تم الحفظ بنجاح.
            </span>
        @endif
    </div>
</form>
