<?php $__env->startSection('title', 'المحاسبة'); ?>
<?php $__env->startSection('page-title', 'نظام المحاسبة'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">المحاسبة</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Statistics -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($stats['total_accounts']); ?></h3>
                <p>إجمالي الحسابات</p>
            </div>
            <div class="icon">
                <i class="fas fa-university"></i>
            </div>
            <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e(number_format($stats['total_income'], 0)); ?></h3>
                <p>إجمالي الإيرادات</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-up"></i>
            </div>
            <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e(number_format($stats['total_expenses'], 0)); ?></h3>
                <p>إجمالي المصروفات</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-down"></i>
            </div>
            <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e(number_format($stats['total_income'] - $stats['total_expenses'], 0)); ?></h3>
                <p>صافي الربح/الخسارة</p>
            </div>
            <div class="icon">
                <i class="fas fa-calculator"></i>
            </div>
            <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <!-- Accounts -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-university mr-1"></i>
                    الحسابات
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> حساب جديد
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم الحساب</th>
                                <th>النوع</th>
                                <th>الرصيد</th>
                                <th>المعاملات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <strong><?php echo e($account->name); ?></strong>
                                    <br><small class="text-muted"><?php echo e($account->code); ?></small>
                                </td>
                                <td>
                                    <?php if($account->type == 'asset'): ?>
                                        <span class="badge badge-primary">أصل</span>
                                    <?php elseif($account->type == 'liability'): ?>
                                        <span class="badge badge-warning">التزام</span>
                                    <?php elseif($account->type == 'equity'): ?>
                                        <span class="badge badge-info">حقوق ملكية</span>
                                    <?php elseif($account->type == 'revenue'): ?>
                                        <span class="badge badge-success">إيراد</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">مصروف</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="<?php echo e($account->balance >= 0 ? 'text-success' : 'text-danger'); ?>">
                                        <?php echo e(number_format($account->balance, 2)); ?> ريال
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-secondary"><?php echo e($account->transactions_count); ?></span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="4" class="text-center">لا توجد حسابات</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exchange-alt mr-1"></i>
                    المعاملات الأخيرة
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> معاملة جديدة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الحساب</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $recent_transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($transaction->transaction_date->format('d/m/Y')); ?></td>
                                <td>
                                    <strong><?php echo e($transaction->account->name); ?></strong>
                                    <br><small class="text-muted"><?php echo e(Str::limit($transaction->description, 30)); ?></small>
                                </td>
                                <td>
                                    <?php if($transaction->type == 'income'): ?>
                                        <span class="badge badge-success">دخل</span>
                                    <?php elseif($transaction->type == 'expense'): ?>
                                        <span class="badge badge-danger">مصروف</span>
                                    <?php else: ?>
                                        <span class="badge badge-info">تحويل</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="<?php echo e($transaction->type == 'income' ? 'text-success' : 'text-danger'); ?>">
                                        <?php echo e($transaction->type == 'income' ? '+' : '-'); ?><?php echo e(number_format($transaction->amount, 2)); ?> ريال
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="4" class="text-center">لا توجد معاملات</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">إجراءات سريعة</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary btn-block">
                            <i class="fas fa-plus"></i> حساب جديد
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-success btn-block">
                            <i class="fas fa-arrow-up"></i> إيراد جديد
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-danger btn-block">
                            <i class="fas fa-arrow-down"></i> مصروف جديد
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-info btn-block">
                            <i class="fas fa-exchange-alt"></i> تحويل بين الحسابات
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-3">
                        <button type="button" class="btn btn-warning btn-block">
                            <i class="fas fa-file-alt"></i> تقرير مالي
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-secondary btn-block">
                            <i class="fas fa-balance-scale"></i> ميزان المراجعة
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-dark btn-block">
                            <i class="fas fa-chart-line"></i> قائمة الدخل
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-file-export"></i> تصدير البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.small-box .icon {
    font-size: 70px;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/accounting/index.blade.php ENDPATH**/ ?>