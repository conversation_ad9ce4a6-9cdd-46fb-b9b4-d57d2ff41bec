<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    /**
     * Display system settings.
     */
    public function index()
    {
        $settings = $this->getSettings();
        return view('settings.index', compact('settings'));
    }

    /**
     * Update system settings.
     */
    public function update(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'company_name' => 'required|string|max:255',
                'company_email' => 'required|email|max:255',
                'company_phone' => 'required|string|max:20',
                'company_address' => 'required|string|max:500',
                'company_city' => 'required|string|max:100',
                'company_country' => 'required|string|max:100',
                'company_tax_number' => 'nullable|string|max:50',
                'company_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'currency' => 'required|string|max:3',
                'timezone' => 'required|string|max:50',
                'date_format' => 'required|string|max:20',
                'language' => 'required|string|max:10',
                'items_per_page' => 'required|integer|min:5|max:100',
                'low_stock_threshold' => 'required|integer|min:1',
                'enable_notifications' => 'boolean',
                'enable_email_notifications' => 'boolean',
                'enable_sms_notifications' => 'boolean',
                'backup_frequency' => 'required|in:daily,weekly,monthly',
                'maintenance_mode' => 'boolean',
            ]);

            $settings = $request->except(['_token', '_method', 'company_logo']);

            // Handle logo upload
            if ($request->hasFile('company_logo')) {
                // Delete old logo
                $oldLogo = $this->getSetting('company_logo');
                if ($oldLogo && Storage::disk('public')->exists($oldLogo)) {
                    Storage::disk('public')->delete($oldLogo);
                }

                // Store new logo
                $logoPath = $request->file('company_logo')->store('logos', 'public');
                $settings['company_logo'] = $logoPath;
            }

            // Save settings to cache and file
            $this->saveSettings($settings);

            // Success notification
            notify_success('تم حفظ الإعدادات بنجاح', 'حفظ الإعدادات');

            return redirect()->route('settings.index');

        } catch (\Exception $e) {
            // Error notification
            notify_error('فشل في حفظ الإعدادات', 'خطأ في الحفظ');

            return back()->withInput();
        }
    }

    /**
     * Reset settings to default.
     */
    public function reset()
    {
        try {
            $defaultSettings = $this->getDefaultSettings();
            $this->saveSettings($defaultSettings);

            notify_success('تم إعادة تعيين الإعدادات إلى القيم الافتراضية', 'إعادة تعيين');

            return redirect()->route('settings.index');
        } catch (\Exception $e) {
            notify_error('فشل في إعادة تعيين الإعدادات', 'خطأ');
            return back();
        }
    }

    /**
     * Clear system cache.
     */
    public function clearCache()
    {
        try {
            Cache::flush();

            // Clear specific caches
            \Artisan::call('config:clear');
            \Artisan::call('route:clear');
            \Artisan::call('view:clear');

            notify_success('تم مسح ذاكرة التخزين المؤقت بنجاح', 'مسح الكاش');

            return back();
        } catch (\Exception $e) {
            notify_error('فشل في مسح ذاكرة التخزين المؤقت', 'خطأ');
            return back();
        }
    }

    /**
     * Get all settings.
     */
    private function getSettings()
    {
        $defaultSettings = $this->getDefaultSettings();
        $savedSettings = Cache::get('system_settings', []);

        return array_merge($defaultSettings, $savedSettings);
    }

    /**
     * Get single setting.
     */
    private function getSetting($key, $default = null)
    {
        $settings = $this->getSettings();
        return $settings[$key] ?? $default;
    }

    /**
     * Save settings.
     */
    private function saveSettings($settings)
    {
        // Save to cache
        Cache::forever('system_settings', $settings);

        // Save to file as backup
        $settingsPath = storage_path('app/settings.json');
        file_put_contents($settingsPath, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * Get default settings.
     */
    private function getDefaultSettings()
    {
        return [
            // Company Information
            'company_name' => 'OmniFlow ERP',
            'company_email' => '<EMAIL>',
            'company_phone' => '+966 50 123 4567',
            'company_address' => 'الرياض، المملكة العربية السعودية',
            'company_city' => 'الرياض',
            'company_country' => 'المملكة العربية السعودية',
            'company_tax_number' => '',
            'company_logo' => '',

            // System Settings
            'currency' => 'SAR',
            'timezone' => 'Asia/Riyadh',
            'date_format' => 'Y-m-d',
            'language' => 'ar',
            'items_per_page' => 15,
            'low_stock_threshold' => 10,

            // Notifications
            'enable_notifications' => true,
            'enable_email_notifications' => true,
            'enable_sms_notifications' => false,

            // System Maintenance
            'backup_frequency' => 'weekly',
            'maintenance_mode' => false,
        ];
    }

    /**
     * Get system information.
     */
    public function systemInfo()
    {
        $info = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => \DB::select('SELECT VERSION() as version')[0]->version ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'disk_free_space' => $this->formatBytes(disk_free_space('/')),
            'disk_total_space' => $this->formatBytes(disk_total_space('/')),
        ];

        return view('settings.system-info', compact('info'));
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Export settings.
     */
    public function export()
    {
        try {
            $settings = $this->getSettings();
            $filename = 'omniflow_settings_' . date('Y-m-d_H-i-s') . '.json';

            $headers = [
                'Content-Type' => 'application/json',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

                return response()->json($settings, 200, $headers);
        } catch (\Exception $e) {
            notify_error('فشل في تصدير الإعدادات', 'خطأ في التصدير');
            return back();
        }
    }

    /**
     * Import settings.
     */
    public function import(Request $request)
    {
        try {
            $request->validate([
                'settings_file' => 'required|file|mimes:json'
            ]);

            $file = $request->file('settings_file');
            $content = file_get_contents($file->getRealPath());
            $settings = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                notify_error('ملف الإعدادات غير صالح', 'خطأ في الاستيراد');
                return back();
            }

            $this->saveSettings($settings);

            notify_success('تم استيراد الإعدادات بنجاح', 'استيراد الإعدادات');

            return redirect()->route('settings.index');
        } catch (\Exception $e) {
            notify_error('فشل في استيراد الإعدادات', 'خطأ في الاستيراد');
            return back();
        }
    }
}
