<?php $__env->startSection('title', 'مبيعة جديدة'); ?>
<?php $__env->startSection('page-title', 'إضافة مبيعة جديدة'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('sales.index')); ?>">المبيعات</a></li>
    <li class="breadcrumb-item active">مبيعة جديدة</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">بيانات المبيعة</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('sales.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </div>
            <form action="<?php echo e(route('sales.store')); ?>" method="POST" id="saleForm">
                <?php echo csrf_field(); ?>
                <div class="card-body">
                    <div class="row">
                        <!-- Customer Selection -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="customer_id">العميل <span class="text-danger">*</span></label>
                                <select class="form-control <?php $__errorArgs = ['customer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($customer->id); ?>" <?php echo e(old('customer_id') == $customer->id ? 'selected' : ''); ?>>
                                            <?php echo e($customer->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['customer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Sale Date -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sale_date">تاريخ البيع <span class="text-danger">*</span></label>
                                <input type="date" class="form-control <?php $__errorArgs = ['sale_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="sale_date" name="sale_date" value="<?php echo e(old('sale_date', date('Y-m-d'))); ?>" required>
                                <?php $__errorArgs = ['sale_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Payment Method -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="payment_method">طريقة الدفع</label>
                                <select class="form-control <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="payment_method" name="payment_method">
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash" <?php echo e(old('payment_method') == 'cash' ? 'selected' : ''); ?>>نقدي</option>
                                    <option value="credit_card" <?php echo e(old('payment_method') == 'credit_card' ? 'selected' : ''); ?>>بطاقة ائتمان</option>
                                    <option value="bank_transfer" <?php echo e(old('payment_method') == 'bank_transfer' ? 'selected' : ''); ?>>تحويل بنكي</option>
                                    <option value="check" <?php echo e(old('payment_method') == 'check' ? 'selected' : ''); ?>>شيك</option>
                                    <option value="other" <?php echo e(old('payment_method') == 'other' ? 'selected' : ''); ?>>أخرى</option>
                                </select>
                                <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">حالة البيع</label>
                                <select class="form-control <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="status" name="status">
                                    <option value="pending" <?php echo e(old('status', 'pending') == 'pending' ? 'selected' : ''); ?>>في الانتظار</option>
                                    <option value="confirmed" <?php echo e(old('status') == 'confirmed' ? 'selected' : ''); ?>>مؤكد</option>
                                    <option value="shipped" <?php echo e(old('status') == 'shipped' ? 'selected' : ''); ?>>تم الشحن</option>
                                    <option value="delivered" <?php echo e(old('status') == 'delivered' ? 'selected' : ''); ?>>تم التسليم</option>
                                </select>
                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Sale Items -->
                    <div class="row">
                        <div class="col-12">
                            <h5>عناصر المبيعة</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="itemsTable">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>سعر الوحدة</th>
                                            <th>الخصم</th>
                                            <th>الإجمالي</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="itemsBody">
                                        <tr class="item-row">
                                            <td>
                                                <select class="form-control product-select" name="items[0][product_id]" required>
                                                    <option value="">اختر المنتج</option>
                                                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($product->id); ?>" data-price="<?php echo e($product->selling_price); ?>">
                                                            <?php echo e($product->name); ?> (<?php echo e($product->sku); ?>)
                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control quantity-input" name="items[0][quantity]" min="1" value="1" required>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control price-input" name="items[0][unit_price]" step="0.01" min="0" required>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control discount-input" name="items[0][discount_amount]" step="0.01" min="0" value="0">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control total-input" readonly>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm remove-item">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <button type="button" class="btn btn-success" id="addItem">
                                <i class="fas fa-plus"></i> إضافة منتج
                            </button>
                        </div>
                    </div>

                    <!-- Totals -->
                    <div class="row mt-3">
                        <div class="col-md-6 ml-auto">
                            <table class="table">
                                <tr>
                                    <th>المبلغ الإجمالي:</th>
                                    <td><span id="totalAmount">0.00</span> ريال</td>
                                </tr>
                                <tr>
                                    <th>إجمالي الخصم:</th>
                                    <td><span id="totalDiscount">0.00</span> ريال</td>
                                </tr>
                                <tr>
                                    <th>الضريبة (15%):</th>
                                    <td><span id="totalTax">0.00</span> ريال</td>
                                </tr>
                                <tr class="table-active">
                                    <th>المبلغ الصافي:</th>
                                    <th><span id="netAmount">0.00</span> ريال</th>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Hidden inputs for totals -->
                    <input type="hidden" name="total_amount" id="hiddenTotalAmount">
                    <input type="hidden" name="tax_amount" id="hiddenTaxAmount">
                    <input type="hidden" name="discount_amount" id="hiddenDiscountAmount">

                    <!-- Notes -->
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="notes">ملاحظات</label>
                                <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                          id="notes" name="notes" rows="3"><?php echo e(old('notes')); ?></textarea>
                                <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <button type="submit" class="btn btn-primary" id="saveBtn">
                        <i class="fas fa-save"></i> حفظ المبيعة
                    </button>
                    <a href="<?php echo e(route('sales.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let itemIndex = 1;

$(document).ready(function() {
    // Add new item row
    $('#addItem').click(function() {
        addItemRow();
    });

    // Remove item row
    $(document).on('click', '.remove-item', function() {
        if ($('.item-row').length > 1) {
            $(this).closest('.item-row').remove();
            calculateTotals();
        }
    });

    // Product selection change
    $(document).on('change', '.product-select', function() {
        const price = $(this).find(':selected').data('price');
        $(this).closest('.item-row').find('.price-input').val(price);
        calculateRowTotal($(this).closest('.item-row'));
    });

    // Quantity or price change
    $(document).on('input', '.quantity-input, .price-input, .discount-input', function() {
        calculateRowTotal($(this).closest('.item-row'));
    });

    // Initial calculation
    calculateTotals();

    // Form submission with loading
    $('form').on('submit', function(e) {
        const saveBtn = $('#saveBtn');
        saveBtn.prop('disabled', true);
        saveBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');

        // Show loading notification
        showLoading('جاري حفظ المبيعة...');
    });
});

function addItemRow() {
    const newRow = `
        <tr class="item-row">
            <td>
                <select class="form-control product-select" name="items[${itemIndex}][product_id]" required>
                    <option value="">اختر المنتج</option>
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($product->id); ?>" data-price="<?php echo e($product->selling_price); ?>">
                            <?php echo e($product->name); ?> (<?php echo e($product->sku); ?>)
                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </td>
            <td>
                <input type="number" class="form-control quantity-input" name="items[${itemIndex}][quantity]" min="1" value="1" required>
            </td>
            <td>
                <input type="number" class="form-control price-input" name="items[${itemIndex}][unit_price]" step="0.01" min="0" required>
            </td>
            <td>
                <input type="number" class="form-control discount-input" name="items[${itemIndex}][discount_amount]" step="0.01" min="0" value="0">
            </td>
            <td>
                <input type="number" class="form-control total-input" readonly>
            </td>
            <td>
                <button type="button" class="btn btn-danger btn-sm remove-item">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    $('#itemsBody').append(newRow);
    itemIndex++;
}

function calculateRowTotal(row) {
    const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
    const price = parseFloat(row.find('.price-input').val()) || 0;
    const discount = parseFloat(row.find('.discount-input').val()) || 0;

    const total = (quantity * price) - discount;
    row.find('.total-input').val(total.toFixed(2));

    calculateTotals();
}

function calculateTotals() {
    let totalAmount = 0;
    let totalDiscount = 0;

    $('.item-row').each(function() {
        const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
        const price = parseFloat($(this).find('.price-input').val()) || 0;
        const discount = parseFloat($(this).find('.discount-input').val()) || 0;

        totalAmount += quantity * price;
        totalDiscount += discount;
    });

    const taxRate = 0.15; // 15% tax
    const taxAmount = (totalAmount - totalDiscount) * taxRate;
    const netAmount = totalAmount - totalDiscount + taxAmount;

    $('#totalAmount').text(totalAmount.toFixed(2));
    $('#totalDiscount').text(totalDiscount.toFixed(2));
    $('#totalTax').text(taxAmount.toFixed(2));
    $('#netAmount').text(netAmount.toFixed(2));

    $('#hiddenTotalAmount').val(totalAmount.toFixed(2));
    $('#hiddenTaxAmount').val(taxAmount.toFixed(2));
    $('#hiddenDiscountAmount').val(totalDiscount.toFixed(2));
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/sales/create.blade.php ENDPATH**/ ?>