<?php $__env->startSection('title', 'العملاء'); ?>
<?php $__env->startSection('page-title', 'إدارة العملاء'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">العملاء</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة العملاء</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> عميل جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>المدينة</th>
                                <th>نوع العميل</th>
                                <th>عدد المبيعات</th>
                                <th>إجمالي المبيعات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <strong><?php echo e($customer->name); ?></strong>
                                    <?php if($customer->tax_number): ?>
                                        <br><small class="text-muted">ض.ر: <?php echo e($customer->tax_number); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($customer->email ?: '-'); ?></td>
                                <td><?php echo e($customer->phone); ?></td>
                                <td><?php echo e($customer->city ?: '-'); ?></td>
                                <td>
                                    <?php if($customer->customer_type == 'company'): ?>
                                        <span class="badge badge-info">شركة</span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">فرد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-primary"><?php echo e($customer->sales_count); ?></span>
                                </td>
                                <td>
                                    <strong><?php echo e(number_format($customer->getTotalSalesAmount(), 2)); ?> ريال</strong>
                                </td>
                                <td>
                                    <?php if($customer->status == 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('customers.show', $customer->id)); ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('customers.edit', $customer->id)); ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('<?php echo e(route('customers.destroy', $customer->id)); ?>', 'حذف العميل', 'هل أنت متأكد من حذف هذا العميل؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="9" class="text-center">لا توجد عملاء</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($customers->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($customers->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($customers->total()); ?></h3>
                <p>إجمالي العملاء</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($customers->where('status', 'active')->count()); ?></h3>
                <p>العملاء النشطين</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-check"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($customers->where('customer_type', 'company')->count()); ?></h3>
                <p>الشركات</p>
            </div>
            <div class="icon">
                <i class="fas fa-building"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($customers->where('status', 'inactive')->count()); ?></h3>
                <p>غير نشط</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-times"></i>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    console.log('Customers page loaded with notification system');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/customers/index.blade.php ENDPATH**/ ?>