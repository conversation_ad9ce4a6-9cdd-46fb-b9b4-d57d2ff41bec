<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Sales\Sale;
use App\Models\Inventory\Product;
use App\Models\Sales\Customer;
use App\Models\Accounting\Account;
use App\Models\Accounting\Transaction;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Display reports dashboard.
     */
    public function index()
    {
        $stats = [
            'total_sales' => Sale::sum('total_amount'),
            'total_products' => Product::count(),
            'total_customers' => Customer::count(),
            'total_transactions' => Transaction::count(),
            'monthly_sales' => Sale::whereMonth('created_at', now()->month)->sum('total_amount'),
            'monthly_transactions' => Transaction::whereMonth('transaction_date', now()->month)->count(),
        ];

        return view('reports.index', compact('stats'));
    }

    /**
     * Sales reports.
     */
    public function sales(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $salesQuery = Sale::with(['customer', 'user'])
            ->whereBetween('created_at', [$startDate, $endDate]);

        // Filter by customer
        if ($request->filled('customer_id')) {
            $salesQuery->where('customer_id', $request->customer_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $salesQuery->where('status', $request->status);
        }

        $sales = $salesQuery->latest()->paginate(20);

        // Statistics
        $stats = [
            'total_sales' => $salesQuery->sum('total_amount'),
            'total_count' => $salesQuery->count(),
            'average_sale' => $salesQuery->avg('total_amount'),
            'completed_sales' => $salesQuery->where('status', 'completed')->sum('total_amount'),
            'pending_sales' => $salesQuery->where('status', 'pending')->sum('total_amount'),
        ];

        // Monthly data for chart
        $monthlyData = Sale::selectRaw('MONTH(created_at) as month, SUM(total_amount) as total')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $customers = Customer::active()->orderBy('name')->get();

        return view('reports.sales', compact('sales', 'stats', 'monthlyData', 'customers', 'startDate', 'endDate'));
    }

    /**
     * Inventory reports.
     */
    public function inventory(Request $request)
    {
        $productsQuery = Product::with(['category', 'supplier']);

        // Filter by category
        if ($request->filled('category_id')) {
            $productsQuery->where('category_id', $request->category_id);
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $productsQuery->where('supplier_id', $request->supplier_id);
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low_stock':
                    $productsQuery->whereRaw('quantity <= minimum_stock');
                    break;
                case 'out_of_stock':
                    $productsQuery->where('quantity', 0);
                    break;
                case 'in_stock':
                    $productsQuery->where('quantity', '>', 0);
                    break;
            }
        }

        $products = $productsQuery->paginate(20);

        // Statistics
        $stats = [
            'total_products' => Product::count(),
            'total_value' => Product::selectRaw('SUM(quantity * price) as total')->value('total'),
            'low_stock_count' => Product::whereRaw('quantity <= minimum_stock')->count(),
            'out_of_stock_count' => Product::where('quantity', 0)->count(),
            'categories_count' => Product::distinct('category_id')->count(),
        ];

        // Get filter options
        $categories = \App\Models\Category::orderBy('name')->get();
        $suppliers = \App\Models\Supplier::active()->orderBy('name')->get();

        return view('reports.inventory', compact('products', 'stats', 'categories', 'suppliers'));
    }

    /**
     * Financial reports.
     */
    public function financial(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        // Transactions
        $transactionsQuery = Transaction::with(['account'])
            ->whereBetween('transaction_date', [$startDate, $endDate]);

        if ($request->filled('account_id')) {
            $transactionsQuery->where('account_id', $request->account_id);
        }

        if ($request->filled('type')) {
            $transactionsQuery->where('type', $request->type);
        }

        $transactions = $transactionsQuery->latest('transaction_date')->paginate(20);

        // Financial Statistics
        $stats = [
            'total_income' => Transaction::income()
                ->whereBetween('transaction_date', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('amount'),
            'total_expenses' => Transaction::expense()
                ->whereBetween('transaction_date', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('amount'),
            'net_profit' => 0, // Will be calculated
            'total_accounts' => Account::count(),
            'active_accounts' => Account::active()->count(),
        ];

        $stats['net_profit'] = $stats['total_income'] - $stats['total_expenses'];

        // Account balances
        $accountBalances = Account::active()
            ->selectRaw('type, SUM(balance) as total_balance')
            ->groupBy('type')
            ->get()
            ->pluck('total_balance', 'type');

        // Monthly financial data
        $monthlyFinancial = Transaction::selectRaw('
                MONTH(transaction_date) as month,
                type,
                SUM(amount) as total
            ')
            ->whereYear('transaction_date', now()->year)
            ->where('status', 'completed')
            ->groupBy('month', 'type')
            ->orderBy('month')
            ->get()
            ->groupBy('month');

        $accounts = Account::active()->orderBy('name')->get();

        return view('reports.financial', compact(
            'transactions',
            'stats',
            'accountBalances',
            'monthlyFinancial',
            'accounts',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Customer reports.
     */
    public function customers(Request $request)
    {
        $customersQuery = Customer::withCount(['sales'])
            ->withSum('sales', 'total_amount');

        // Filter by type
        if ($request->filled('customer_type')) {
            $customersQuery->where('customer_type', $request->customer_type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $customersQuery->where('status', $request->status);
        }

        $customers = $customersQuery->orderBy('sales_sum_total_amount', 'desc')->paginate(20);

        // Statistics
        $stats = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::where('status', 'active')->count(),
            'company_customers' => Customer::where('customer_type', 'company')->count(),
            'individual_customers' => Customer::where('customer_type', 'individual')->count(),
            'customers_with_sales' => Customer::whereHas('sales')->count(),
        ];

        return view('reports.customers', compact('customers', 'stats'));
    }

    /**
     * Export sales report to CSV.
     */
    public function exportSales(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

        $sales = Sale::with(['customer', 'user'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $filename = 'sales_report_' . $startDate . '_to_' . $endDate . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($sales) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Headers
            fputcsv($file, [
                'رقم المبيعة',
                'العميل',
                'المبلغ الإجمالي',
                'الحالة',
                'تاريخ الإنشاء',
                'المستخدم'
            ]);

            foreach ($sales as $sale) {
                fputcsv($file, [
                    $sale->id,
                    $sale->customer->name ?? 'غير محدد',
                    $sale->total_amount,
                    $sale->status,
                    $sale->created_at->format('Y-m-d H:i:s'),
                    $sale->user->name ?? 'غير محدد'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
