@extends('layouts.auth')

@section('title', 'تسجيل الدخول')

@section('content')
<div class="card">
    <div class="card-header">
        <div class="login-logo">
            <i class="fas fa-chart-line"></i>
            <strong>OmniFlow</strong> ERP
        </div>
        <p class="text-muted">تسجيل الدخول إلى حسابك</p>
    </div>

    <div class="card-body">
        <!-- Session Status -->
        @if (session('status'))
            <div class="alert alert-success" role="alert">
                {{ session('status') }}
            </div>
        @endif

        <form method="POST" action="{{ route('login') }}">
            @csrf

            <!-- Email Address -->
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                    </div>
                    <input type="email"
                           class="form-control @error('email') is-invalid @enderror"
                           name="email"
                           value="{{ old('email') }}"
                           placeholder="البريد الإلكتروني"
                           required
                           autofocus
                           autocomplete="username">
                </div>
                @error('email')
                    <span class="invalid-feedback d-block">{{ $message }}</span>
                @enderror
            </div>

            <!-- Password -->
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                    <input type="password"
                           class="form-control @error('password') is-invalid @enderror"
                           name="password"
                           placeholder="كلمة المرور"
                           required
                           autocomplete="current-password">
                </div>
                @error('password')
                    <span class="invalid-feedback d-block">{{ $message }}</span>
                @enderror
            </div>

            <!-- Remember Me -->
            <div class="form-group">
                <div class="icheck-primary">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">تذكرني</label>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    تسجيل الدخول
                </button>
            </div>

            <!-- Links -->
            <div class="text-center">
                @if (Route::has('password.request'))
                    <a href="{{ route('password.request') }}" class="text-link">
                        نسيت كلمة المرور؟
                    </a>
                @endif

                @if (Route::has('register'))
                    <br>
                    <a href="{{ route('register') }}" class="text-link">
                        إنشاء حساب جديد
                    </a>
                @endif
            </div>
        </form>
    </div>
</div>
@endsection
