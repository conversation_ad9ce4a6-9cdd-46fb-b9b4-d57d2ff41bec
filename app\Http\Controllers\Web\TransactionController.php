<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Accounting\Transaction;
use App\Models\Accounting\Account;
use Illuminate\Http\Request;

class TransactionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Transaction::with(['account', 'user'])
            ->latest('transaction_date');

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by account
        if ($request->filled('account_id')) {
            $query->where('account_id', $request->account_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('transaction_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('transaction_date', '<=', $request->date_to);
        }

        $transactions = $query->paginate(15);

        // Get accounts for filter dropdown
        $accounts = Account::active()->orderBy('name')->get();

        return view('transactions.index', compact('transactions', 'accounts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $accounts = Account::active()->orderBy('name')->get();
        return view('transactions.create', compact('accounts'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'account_id' => 'required|exists:accounts,id',
                'type' => 'required|in:income,expense,transfer',
                'amount' => 'required|numeric|min:0.01',
                'description' => 'required|string|max:255',
                'transaction_date' => 'required|date',
                'payment_method' => 'required|in:cash,credit_card,bank_transfer,check,other',
                'status' => 'required|in:pending,completed,cancelled',
                'receipt_number' => 'nullable|string|max:50',
                'invoice_number' => 'nullable|string|max:50',
                'check_number' => 'nullable|string|max:50',
                'bank_reference' => 'nullable|string|max:100',
                'notes' => 'nullable|string|max:1000'
            ]);

            $data = $request->all();
            $data['user_id'] = auth()->id();

            // Generate reference number
            $data['reference_number'] = $this->generateReferenceNumber($data['type']);

            // Create transaction
            $transaction = Transaction::create($data);

            // Update account balance if transaction is completed
            if ($transaction->status === 'completed') {
                $this->updateAccountBalance($transaction);
            }

            // Success notification
            notify_crud('created', 'transaction');

            return redirect()->route('transactions.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'transaction', false);

            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Transaction $transaction)
    {
        $transaction->load(['account', 'user', 'reconciledBy']);
        return view('transactions.show', compact('transaction'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Transaction $transaction)
    {
        $accounts = Account::active()->orderBy('name')->get();
        return view('transactions.edit', compact('transaction', 'accounts'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Transaction $transaction)
    {
        try {
            // Validation
            $request->validate([
                'account_id' => 'required|exists:accounts,id',
                'type' => 'required|in:income,expense,transfer',
                'amount' => 'required|numeric|min:0.01',
                'description' => 'required|string|max:255',
                'transaction_date' => 'required|date',
                'payment_method' => 'required|in:cash,credit_card,bank_transfer,check,other',
                'status' => 'required|in:pending,completed,cancelled',
                'receipt_number' => 'nullable|string|max:50',
                'invoice_number' => 'nullable|string|max:50',
                'check_number' => 'nullable|string|max:50',
                'bank_reference' => 'nullable|string|max:100',
                'notes' => 'nullable|string|max:1000'
            ]);

            $oldStatus = $transaction->status;
            $oldAmount = $transaction->amount;
            $oldType = $transaction->type;
            $oldAccountId = $transaction->account_id;

            // Update transaction
            $transaction->update($request->all());

            // Update account balance if status or amount changed
            if ($oldStatus !== $transaction->status ||
                $oldAmount != $transaction->amount ||
                $oldType !== $transaction->type ||
                $oldAccountId != $transaction->account_id) {

                // Reverse old transaction effect
                if ($oldStatus === 'completed') {
                    $this->reverseAccountBalance($oldAccountId, $oldType, $oldAmount);
                }

                // Apply new transaction effect
                if ($transaction->status === 'completed') {
                    $this->updateAccountBalance($transaction);
                }
            }

            // Success notification
            notify_crud('updated', 'transaction');

            return redirect()->route('transactions.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'transaction', false);

            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Transaction $transaction)
    {
        try {
            // Reverse account balance if transaction was completed
            if ($transaction->status === 'completed') {
                $this->reverseAccountBalance(
                    $transaction->account_id,
                    $transaction->type,
                    $transaction->amount
                );
            }

            $transaction->delete();

            // Success notification
            notify_crud('deleted', 'transaction');

            return redirect()->route('transactions.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'transaction', false);

            return back();
        }
    }

    /**
     * Mark transaction as reconciled.
     */
    public function reconcile(Transaction $transaction)
    {
        try {
            $transaction->markAsReconciled();

            notify_success('تم تسوية المعاملة بنجاح', 'تسوية المعاملة');

            return back();
        } catch (\Exception $e) {
            notify_error('فشل في تسوية المعاملة', 'خطأ في التسوية');
            return back();
        }
    }

    /**
     * Mark transaction as unreconciled.
     */
    public function unreconcile(Transaction $transaction)
    {
        try {
            $transaction->markAsUnreconciled();

            notify_success('تم إلغاء تسوية المعاملة بنجاح', 'إلغاء التسوية');

            return back();
        } catch (\Exception $e) {
            notify_error('فشل في إلغاء تسوية المعاملة', 'خطأ في إلغاء التسوية');
            return back();
        }
    }

    /**
     * Update account balance based on transaction.
     */
    private function updateAccountBalance(Transaction $transaction)
    {
        $account = Account::find($transaction->account_id);

        if ($account) {
            if ($transaction->type === 'income') {
                $account->increment('balance', $transaction->amount);
            } elseif ($transaction->type === 'expense') {
                $account->decrement('balance', $transaction->amount);
            }
        }
    }

    /**
     * Reverse account balance effect.
     */
    private function reverseAccountBalance($accountId, $type, $amount)
    {
        $account = Account::find($accountId);

        if ($account) {
            if ($type === 'income') {
                $account->decrement('balance', $amount);
            } elseif ($type === 'expense') {
                $account->increment('balance', $amount);
            }
        }
    }

    /**
     * Generate reference number.
     */
    private function generateReferenceNumber($type)
    {
        $prefix = [
            'income' => 'INC',
            'expense' => 'EXP',
            'transfer' => 'TRF'
        ];

        $typePrefix = $prefix[$type] ?? 'TXN';
        $number = str_pad(Transaction::where('type', $type)->count() + 1, 6, '0', STR_PAD_LEFT);

        return $typePrefix . '-' . date('Ymd') . '-' . $number;
    }

    /**
     * Get transaction statistics.
     */
    public function getStats()
    {
        $stats = [
            'total_transactions' => Transaction::count(),
            'completed_transactions' => Transaction::completed()->count(),
            'pending_transactions' => Transaction::where('status', 'pending')->count(),
            'total_income' => Transaction::income()->completed()->sum('amount'),
            'total_expenses' => Transaction::expense()->completed()->sum('amount'),
            'reconciled_transactions' => Transaction::reconciled()->count(),
            'unreconciled_transactions' => Transaction::unreconciled()->count()
        ];

        return response()->json($stats);
    }
}
