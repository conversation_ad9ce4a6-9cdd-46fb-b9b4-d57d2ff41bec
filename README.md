# 🚀 OmniFlow ERP 2025

<div align="center">

![OmniFlow ERP](https://img.shields.io/badge/OmniFlow-ERP%202025-blue?style=for-the-badge)
![Laravel](https://img.shields.io/badge/Laravel-12.0-red?style=for-the-badge&logo=laravel)
![PHP](https://img.shields.io/badge/PHP-8.2+-777BB4?style=for-the-badge&logo=php)
![Status](https://img.shields.io/badge/Status-Production%20Ready-green?style=for-the-badge)

**نظام تخطيط موارد المؤسسات الشامل والمتطور**

[🌟 الميزات](#-الميزات) • [⚡ التثبيت](#-التثبيت-السريع) • [📖 التوثيق](#-التوثيق) • [🎯 الاستخدام](#-الاستخدام)

</div>

---

## 📋 نظرة عامة

**OmniFlow ERP 2025** هو نظام تخطيط موارد المؤسسات (ERP) شامل ومتطور، مبني بتقنية Laravel 12 ومصمم خصيصاً للشركات العربية. يوفر النظام حلولاً متكاملة لإدارة المبيعات والمخزون والمحاسبة مع دعم كامل للغة العربية.

### 🎯 الهدف من النظام
- **إدارة شاملة** للعمليات التجارية
- **واجهة عربية** سهلة الاستخدام
- **تقارير متقدمة** وإحصائيات تفصيلية
- **نظام صلاحيات** متطور ومرن
- **API متكامل** للتطبيقات الخارجية

---

## 🌟 الميزات الرئيسية

### 💼 إدارة المبيعات
- ✅ **إدارة العملاء** - قاعدة بيانات شاملة للعملاء
- ✅ **معالجة الطلبات** - نظام طلبات متكامل
- ✅ **تتبع المدفوعات** - إدارة المدفوعات والفواتير
- ✅ **تقارير المبيعات** - تحليلات مفصلة للأداء

### 📦 إدارة المخزون
- ✅ **إدارة المنتجات** - كتالوج منتجات شامل
- ✅ **تتبع المخزون** - مراقبة الكميات والحركة
- ✅ **إدارة الموردين** - قاعدة بيانات الموردين
- ✅ **تنبيهات المخزون** - تحذيرات المخزون المنخفض

### 💰 النظام المحاسبي
- ✅ **إدارة الحسابات** - نظام حسابات متكامل
- ✅ **المعاملات المالية** - تسجيل وتتبع المعاملات
- ✅ **التقارير المالية** - ميزانيات وتقارير ربح وخسارة
- ✅ **إدارة النقدية** - تتبع التدفقات النقدية

### 👥 إدارة المستخدمين
- ✅ **نظام الأدوار** - أدوار مخصصة للمستخدمين
- ✅ **الصلاحيات** - تحكم دقيق في الوصول
- ✅ **أمان متقدم** - حماية البيانات والمعلومات
- ✅ **سجل الأنشطة** - تتبع جميع العمليات

---

## 🛠 التقنيات المستخدمة

### Backend
- **Laravel 12.0** - إطار العمل الرئيسي
- **PHP 8.2+** - لغة البرمجة
- **MySQL** - قاعدة البيانات
- **Spatie Permissions** - نظام الصلاحيات
- **Laravel Sanctum** - المصادقة

### Frontend
- **AdminLTE 3** - واجهة الإدارة
- **Bootstrap 4** - إطار العمل للتصميم
- **jQuery** - مكتبة JavaScript
- **SweetAlert2** - الإشعارات التفاعلية
- **Chart.js** - الرسوم البيانية

### أدوات إضافية
- **DomPDF** - تصدير PDF
- **Fast Excel** - تصدير Excel
- **Vite** - أداة البناء
- **Tailwind CSS** - تنسيق إضافي

---

## ⚡ التثبيت السريع

### المتطلبات الأساسية
```bash
- PHP 8.2 أو أحدث
- Composer
- Node.js & NPM
- MySQL 8.0+
- Git
```

### خطوات التثبيت

#### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/omniflow-erp-2025.git
cd omniflow-erp-2025
```

#### 2. تثبيت التبعيات
```bash
# تثبيت حزم PHP
composer install

# تثبيت حزم Node.js
npm install
```

#### 3. إعداد البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate
```

#### 4. إعداد قاعدة البيانات
```bash
# تحديث ملف .env بمعلومات قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=omniflow_erp2025
DB_USERNAME=your_username
DB_PASSWORD=your_password

# تشغيل الهجرات
php artisan migrate

# إدخال البيانات الأساسية
php artisan db:seed
```

#### 5. بناء الأصول
```bash
# بناء ملفات CSS و JavaScript
npm run build
```

#### 6. تشغيل الخادم
```bash
php artisan serve
```

🎉 **تهانينا!** النظام جاهز على: `http://localhost:8000`

---

## 🔐 بيانات الدخول الافتراضية

### المدير العام
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `password`
- **الدور:** Super Admin

### أدوار أخرى متاحة
- **Admin** - مدير عام
- **Sales Manager** - مدير المبيعات
- **Inventory Manager** - مدير المخزون
- **Accountant** - محاسب
- **Sales Rep** - مندوب مبيعات
- **User** - مستخدم عادي

---

## 📖 التوثيق التفصيلي

### 🗂 هيكل المشروع
```
OmniFlow-ERP2025/
├── app/
│   ├── Http/Controllers/
│   │   ├── API/V1/          # API Controllers
│   │   └── Web/             # Web Controllers
│   ├── Models/
│   │   ├── Sales/           # نماذج المبيعات
│   │   ├── Inventory/       # نماذج المخزون
│   │   └── Accounting/      # نماذج المحاسبة
│   └── Services/            # خدمات الأعمال
├── database/
│   ├── migrations/          # هجرات قاعدة البيانات
│   └── seeders/            # بيانات أولية
├── resources/
│   ├── views/              # قوالب Blade
│   ├── js/                 # ملفات JavaScript
│   └── css/                # ملفات التنسيق
└── routes/
    ├── web.php             # مسارات الويب
    └── api.php             # مسارات API
```

### 📊 قاعدة البيانات

#### الجداول الرئيسية (20 جدول)
- **users** - المستخدمين
- **customers** - العملاء
- **products** - المنتجات
- **categories** - التصنيفات
- **suppliers** - الموردين
- **sales** - المبيعات
- **sale_items** - عناصر المبيعات
- **accounts** - الحسابات المالية
- **transactions** - المعاملات المالية
- **permissions** - الصلاحيات
- **roles** - الأدوار

#### العلاقات الرئيسية
- Customer → Sales (One to Many)
- Product → Sale Items (One to Many)
- Category → Products (One to Many)
- Supplier → Products (One to Many)
- Account → Transactions (One to Many)

---

## 🎯 الاستخدام

### 🏠 لوحة التحكم
الصفحة الرئيسية تعرض:
- **إحصائيات المبيعات** - يومية وشهرية
- **حالة المخزون** - المنتجات المنخفضة والنافدة
- **الأرصدة المالية** - أرصدة الحسابات
- **أنشطة حديثة** - آخر العمليات

### 💼 إدارة المبيعات
```
/sales - قائمة المبيعات
/sales/create - إنشاء مبيعة جديدة
/customers - إدارة العملاء
/customers/create - إضافة عميل جديد
```

### 📦 إدارة المخزون
```
/products - قائمة المنتجات
/products/create - إضافة منتج جديد
/categories - إدارة التصنيفات
/suppliers - إدارة الموردين
```

### 💰 المحاسبة
```
/accounts - الحسابات المالية
/transactions - المعاملات المالية
/reports/financial - التقارير المالية
```

### 📊 التقارير
```
/reports - صفحة التقارير الرئيسية
/reports/sales - تقارير المبيعات
/reports/inventory - تقارير المخزون
/reports/financial - التقارير المالية
/reports/customers - تقارير العملاء
```

---

## 🔌 API Documentation

### Base URL
```
http://localhost:8000/api/v1
```

### Authentication
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

### Sales Endpoints
```bash
GET    /api/v1/sales              # جلب المبيعات
POST   /api/v1/sales              # إنشاء مبيعة
GET    /api/v1/sales/{id}         # جلب مبيعة محددة
PUT    /api/v1/sales/{id}         # تحديث مبيعة
DELETE /api/v1/sales/{id}         # حذف مبيعة
```

### Inventory Endpoints
```bash
GET    /api/v1/inventory          # جلب المنتجات
POST   /api/v1/inventory          # إنشاء منتج
PUT    /api/v1/inventory/{id}     # تحديث منتج
DELETE /api/v1/inventory/{id}     # حذف منتج
```

### Accounting Endpoints
```bash
GET    /api/v1/accounting/transactions    # جلب المعاملات
POST   /api/v1/accounting/transactions    # إنشاء معاملة
GET    /api/v1/accounting/reports         # التقارير المالية
```

---

## 🚀 الميزات المتقدمة

### 🔒 نظام الصلاحيات
- **70+ صلاحية** مختلفة
- **7 أدوار** أساسية
- **تحكم دقيق** في الوصول
- **وراثة الصلاحيات** بين الأدوار

### 📱 التصميم المتجاوب
- **دعم كامل للهواتف** المحمولة
- **واجهة RTL** للغة العربية
- **تصميم حديث** وسهل الاستخدام

### 📈 التقارير والإحصائيات
- **تقارير PDF** قابلة للطباعة
- **تصدير Excel** للبيانات
- **رسوم بيانية** تفاعلية
- **فلترة متقدمة** للبيانات

### 🔔 نظام الإشعارات
- **إشعارات فورية** للعمليات
- **تنبيهات المخزون** المنخفض
- **تأكيدات العمليات** الحساسة

---

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# جميع الاختبارات
php artisan test

# اختبارات محددة
php artisan test --filter=SalesTest
```

### اختبار API
```bash
# اختبار endpoint المبيعات
curl -X GET http://localhost:8000/api/v1/sales \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## 🔧 التخصيص والتطوير

### إضافة وحدة جديدة
1. إنشاء Model في المجلد المناسب
2. إنشاء Migration لقاعدة البيانات
3. إنشاء Controller للعمليات
4. إضافة Routes للمسارات
5. إنشاء Views للواجهة

### تخصيص التصميم
- تعديل ملفات CSS في `resources/css/`
- تخصيص قوالب Blade في `resources/views/`
- إضافة JavaScript في `resources/js/`

---

## 📞 الدعم والمساعدة

### 🐛 الإبلاغ عن الأخطاء
إذا واجهت أي مشكلة، يرجى:
1. التحقق من [الأسئلة الشائعة](#)
2. البحث في [المشاكل المعروفة](#)
3. إنشاء [تقرير خطأ جديد](#)

### 💡 طلب ميزات جديدة
لطلب ميزات جديدة:
1. تحقق من [خارطة الطريق](#)
2. اقترح الميزة في [المناقشات](#)

### 📧 التواصل
- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** [www.omniflow.com](#)

---

## 📄 الترخيص

هذا المشروع مرخص تحت [رخصة MIT](LICENSE) - راجع ملف LICENSE للتفاصيل.

---

## 🙏 شكر وتقدير

شكر خاص لـ:
- **Laravel Team** - إطار العمل الرائع
- **AdminLTE** - واجهة الإدارة الجميلة
- **Spatie** - حزم Laravel المفيدة
- **المجتمع العربي** - الدعم والمساهمات

---

<div align="center">

**صُنع بـ ❤️ للمجتمع العربي**

[⭐ ضع نجمة](../../stargazers) • [🍴 Fork](../../fork) • [📝 ساهم](../../issues)

</div>
