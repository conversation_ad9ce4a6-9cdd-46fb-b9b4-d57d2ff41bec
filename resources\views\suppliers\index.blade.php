@extends('layouts.app')

@section('title', 'الموردين')
@section('page-title', 'إدارة الموردين')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">الموردين</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة الموردين</h3>
                <div class="card-tools">
                    <a href="{{ route('suppliers.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> مورد جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الاسم / الشركة</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>المدينة</th>
                                <th>نوع المورد</th>
                                <th>عدد المنتجات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($suppliers as $supplier)
                            <tr>
                                <td>
                                    <strong>{{ $supplier->company_name ?: $supplier->name }}</strong>
                                    @if($supplier->company_name && $supplier->name)
                                        <br><small class="text-muted">{{ $supplier->name }}</small>
                                    @endif
                                    @if($supplier->tax_number)
                                        <br><small class="text-muted">ض.ر: {{ $supplier->tax_number }}</small>
                                    @endif
                                </td>
                                <td>{{ $supplier->email ?: '-' }}</td>
                                <td>
                                    {{ $supplier->phone }}
                                    @if($supplier->mobile)
                                        <br><small class="text-muted">{{ $supplier->mobile }}</small>
                                    @endif
                                </td>
                                <td>{{ $supplier->city ?: '-' }}</td>
                                <td>
                                    @switch($supplier->supplier_type)
                                        @case('company')
                                            <span class="badge badge-info">شركة</span>
                                            @break
                                        @case('individual')
                                            <span class="badge badge-secondary">فرد</span>
                                            @break
                                        @case('government')
                                            <span class="badge badge-warning">جهة حكومية</span>
                                            @break
                                        @case('international')
                                            <span class="badge badge-primary">دولي</span>
                                            @break
                                        @default
                                            <span class="badge badge-light">{{ $supplier->supplier_type }}</span>
                                    @endswitch
                                </td>
                                <td>
                                    <span class="badge badge-primary">{{ $supplier->products_count }}</span>
                                </td>
                                <td>
                                    @if($supplier->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @else
                                        <span class="badge badge-danger">غير نشط</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('suppliers.show', $supplier->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('suppliers.edit', $supplier->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('{{ route('suppliers.destroy', $supplier->id) }}', 'حذف المورد', 'هل أنت متأكد من حذف هذا المورد؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">لا توجد موردين</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($suppliers->hasPages())
            <div class="card-footer">
                {{ $suppliers->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $suppliers->total() }}</h3>
                <p>إجمالي الموردين</p>
            </div>
            <div class="icon">
                <i class="fas fa-truck"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $suppliers->where('status', 'active')->count() }}</h3>
                <p>الموردين النشطين</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $suppliers->where('supplier_type', 'company')->count() }}</h3>
                <p>الشركات</p>
            </div>
            <div class="icon">
                <i class="fas fa-building"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $suppliers->where('supplier_type', 'international')->count() }}</h3>
                <p>موردين دوليين</p>
            </div>
            <div class="icon">
                <i class="fas fa-globe"></i>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    console.log('Suppliers page loaded with notification system');
});
</script>
@endpush
