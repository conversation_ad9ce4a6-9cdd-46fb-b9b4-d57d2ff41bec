@extends('layouts.app')

@section('title', 'التصنيفات')
@section('page-title', 'إدارة التصنيفات')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">التصنيفات</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة التصنيفات</h3>
                <div class="card-tools">
                    <a href="{{ route('categories.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> تصنيف جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>التصنيف الأب</th>
                                <th>عدد المنتجات</th>
                                <th>ترتيب العرض</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($categories as $category)
                            <tr>
                                <td>
                                    @if($category->image)
                                        <img src="{{ asset('storage/categories/' . $category->image) }}" 
                                             alt="{{ $category->name }}" 
                                             class="img-thumbnail" 
                                             style="width: 50px; height: 50px; object-fit: cover;">
                                    @else
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px; border-radius: 4px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    <strong>{{ $category->name }}</strong>
                                    @if($category->description)
                                        <br><small class="text-muted">{{ Str::limit($category->description, 50) }}</small>
                                    @endif
                                </td>
                                <td>
                                    @if($category->parent)
                                        <span class="badge badge-info">{{ $category->parent->name }}</span>
                                    @else
                                        <span class="badge badge-secondary">تصنيف رئيسي</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge-primary">{{ $category->products_count }}</span>
                                    @if($category->children()->count() > 0)
                                        <br><small class="text-muted">{{ $category->children()->count() }} تصنيف فرعي</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge-light">{{ $category->sort_order }}</span>
                                </td>
                                <td>
                                    @if($category->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @else
                                        <span class="badge badge-danger">غير نشط</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('categories.show', $category->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('categories.edit', $category->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('{{ route('categories.destroy', $category->id) }}', 'حذف التصنيف', 'هل أنت متأكد من حذف هذا التصنيف؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center">لا توجد تصنيفات</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($categories->hasPages())
            <div class="card-footer">
                {{ $categories->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $categories->total() }}</h3>
                <p>إجمالي التصنيفات</p>
            </div>
            <div class="icon">
                <i class="fas fa-tags"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $categories->where('status', 'active')->count() }}</h3>
                <p>التصنيفات النشطة</p>
            </div>
            <div class="icon">
                <i class="fas fa-tag"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $categories->whereNull('parent_id')->count() }}</h3>
                <p>التصنيفات الرئيسية</p>
            </div>
            <div class="icon">
                <i class="fas fa-folder"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $categories->whereNotNull('parent_id')->count() }}</h3>
                <p>التصنيفات الفرعية</p>
            </div>
            <div class="icon">
                <i class="fas fa-folder-open"></i>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    console.log('Categories page loaded with notification system');
});
</script>
@endpush
