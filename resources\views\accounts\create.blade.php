@extends('layouts.app')

@section('title', 'إضافة حساب جديد')
@section('page-title', 'إضافة حساب محاسبي جديد')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="{{ route('accounting.index') }}">المحاسبة</a></li>
    <li class="breadcrumb-item"><a href="{{ route('accounts.index') }}">الحسابات</a></li>
    <li class="breadcrumb-item active">إضافة حساب جديد</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">بيانات الحساب الجديد</h3>
            </div>
            
            <form action="{{ route('accounts.store') }}" method="POST">
                @csrf
                <div class="card-body">
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">اسم الحساب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="code">كود الحساب</label>
                                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                       id="code" name="code" value="{{ old('code') }}" 
                                       placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                                @error('code')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                                <small class="form-text text-muted">مثال: AST-CAS-001</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="type">نوع الحساب <span class="text-danger">*</span></label>
                                <select class="form-control @error('type') is-invalid @enderror" 
                                        id="type" name="type" required>
                                    <option value="">اختر نوع الحساب</option>
                                    <option value="asset" {{ old('type') == 'asset' ? 'selected' : '' }}>أصول (Assets)</option>
                                    <option value="liability" {{ old('type') == 'liability' ? 'selected' : '' }}>التزامات (Liabilities)</option>
                                    <option value="equity" {{ old('type') == 'equity' ? 'selected' : '' }}>حقوق ملكية (Equity)</option>
                                    <option value="revenue" {{ old('type') == 'revenue' ? 'selected' : '' }}>إيرادات (Revenue)</option>
                                    <option value="expense" {{ old('type') == 'expense' ? 'selected' : '' }}>مصروفات (Expenses)</option>
                                </select>
                                @error('type')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="currency">العملة <span class="text-danger">*</span></label>
                                <select class="form-control @error('currency') is-invalid @enderror" 
                                        id="currency" name="currency" required>
                                    <option value="SAR" {{ old('currency', 'SAR') == 'SAR' ? 'selected' : '' }}>ريال سعودي (SAR)</option>
                                    <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                                    <option value="EUR" {{ old('currency') == 'EUR' ? 'selected' : '' }}>يورو (EUR)</option>
                                    <option value="AED" {{ old('currency') == 'AED' ? 'selected' : '' }}>درهم إماراتي (AED)</option>
                                </select>
                                @error('currency')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="balance">الرصيد الابتدائي</label>
                                <input type="number" class="form-control @error('balance') is-invalid @enderror" 
                                       id="balance" name="balance" value="{{ old('balance', 0) }}" 
                                       step="0.01">
                                @error('balance')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">الحالة <span class="text-danger">*</span></label>
                                <select class="form-control @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                </select>
                                @error('status')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Bank Information -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title">معلومات البنك (اختيارية)</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="bank_name">اسم البنك</label>
                                        <input type="text" class="form-control @error('bank_name') is-invalid @enderror" 
                                               id="bank_name" name="bank_name" value="{{ old('bank_name') }}">
                                        @error('bank_name')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="account_number">رقم الحساب</label>
                                        <input type="text" class="form-control @error('account_number') is-invalid @enderror" 
                                               id="account_number" name="account_number" value="{{ old('account_number') }}">
                                        @error('account_number')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="routing_number">رقم التوجيه / IBAN</label>
                                        <input type="text" class="form-control @error('routing_number') is-invalid @enderror" 
                                               id="routing_number" name="routing_number" value="{{ old('routing_number') }}">
                                        @error('routing_number')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check mt-4">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="is_default" name="is_default" value="1" 
                                                   {{ old('is_default') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_default">
                                                جعل هذا الحساب افتراضي لنوعه
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description and Notes -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="description">الوصف</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3">{{ old('description') }}</textarea>
                                @error('description')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="notes">ملاحظات</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <button type="submit" class="btn btn-primary" id="saveBtn">
                        <i class="fas fa-save"></i> حفظ الحساب
                    </button>
                    <a href="{{ route('accounts.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission with loading
    $('form').on('submit', function(e) {
        const saveBtn = $('#saveBtn');
        saveBtn.prop('disabled', true);
        saveBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        
        showLoading('جاري حفظ بيانات الحساب...');
    });

    // Auto-generate code based on name and type
    $('#name, #type').on('change', function() {
        if ($('#code').val() === '') {
            generateAccountCode();
        }
    });

    function generateAccountCode() {
        const name = $('#name').val();
        const type = $('#type').val();
        
        if (name && type) {
            const typePrefix = {
                'asset': 'AST',
                'liability': 'LIB',
                'equity': 'EQT',
                'revenue': 'REV',
                'expense': 'EXP'
            };
            
            const nameCode = name.replace(/[^A-Za-z]/g, '').substring(0, 3).toUpperCase();
            const prefix = typePrefix[type] || 'ACC';
            const number = String(Math.floor(Math.random() * 999) + 1).padStart(3, '0');
            
            $('#code').val(prefix + '-' + nameCode + '-' + number);
        }
    }
});
</script>
@endpush
