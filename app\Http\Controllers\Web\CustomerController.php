<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Sales\Customer;
use Illuminate\Http\Request;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $customers = Customer::with('sales')
            ->withCount('sales')
            ->latest()
            ->paginate(15);

        return view('customers.index', compact('customers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('customers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:customers,email',
                'phone' => 'required|string|max:20',
                'address' => 'nullable|string|max:500',
                'city' => 'nullable|string|max:100',
                'country' => 'nullable|string|max:100',
                'postal_code' => 'nullable|string|max:20',
                'tax_number' => 'nullable|string|max:50',
                'credit_limit' => 'nullable|numeric|min:0',
                'payment_terms' => 'nullable|integer|min:0|max:365',
                'status' => 'required|in:active,inactive',
                'customer_type' => 'nullable|in:individual,company',
                'notes' => 'nullable|string|max:1000'
            ]);

            // Create customer
            $customer = Customer::create($request->all());

            // Success notification
            notify_crud('created', 'customer');

            return redirect()->route('customers.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'customer', false);
            
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Customer $customer)
    {
        $customer->load(['sales' => function($query) {
            $query->latest('sale_date')->take(10);
        }]);

        $stats = [
            'total_sales' => $customer->getTotalSalesAmount(),
            'sales_count' => $customer->getTotalSalesCount(),
            'average_sale' => $customer->getAverageSaleAmount(),
            'outstanding_balance' => $customer->getOutstandingBalance(),
            'last_sale_date' => $customer->sales()->latest('sale_date')->first()?->sale_date
        ];

        return view('customers.show', compact('customer', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Customer $customer)
    {
        return view('customers.edit', compact('customer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:customers,email,' . $customer->id,
                'phone' => 'required|string|max:20',
                'address' => 'nullable|string|max:500',
                'city' => 'nullable|string|max:100',
                'country' => 'nullable|string|max:100',
                'postal_code' => 'nullable|string|max:20',
                'tax_number' => 'nullable|string|max:50',
                'credit_limit' => 'nullable|numeric|min:0',
                'payment_terms' => 'nullable|integer|min:0|max:365',
                'status' => 'required|in:active,inactive',
                'customer_type' => 'nullable|in:individual,company',
                'notes' => 'nullable|string|max:1000'
            ]);

            // Update customer
            $customer->update($request->all());

            // Success notification
            notify_crud('updated', 'customer');

            return redirect()->route('customers.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'customer', false);
            
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Customer $customer)
    {
        try {
            // Check if customer has sales
            if ($customer->sales()->count() > 0) {
                notify_error('لا يمكن حذف العميل لأنه يحتوي على مبيعات', 'خطأ في الحذف');
                return back();
            }

            $customer->delete();

            // Success notification
            notify_crud('deleted', 'customer');

            return redirect()->route('customers.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'customer', false);
            
            return back();
        }
    }

    /**
     * Restore a soft deleted customer.
     */
    public function restore($id)
    {
        try {
            $customer = Customer::withTrashed()->findOrFail($id);
            $customer->restore();

            notify_crud('restored', 'customer');

            return redirect()->route('customers.index');

        } catch (\Exception $e) {
            notify_crud('restored', 'customer', false);
            return back();
        }
    }

    /**
     * Get customer statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::active()->count(),
            'inactive_customers' => Customer::where('status', 'inactive')->count(),
            'customers_with_credit_limit' => Customer::where('credit_limit', '>', 0)->count(),
            'customers_exceeded_limit' => Customer::where('credit_limit', '>', 0)
                ->get()
                ->filter(function($customer) {
                    return $customer->hasExceededCreditLimit();
                })
                ->count()
        ];

        return response()->json($stats);
    }
}
