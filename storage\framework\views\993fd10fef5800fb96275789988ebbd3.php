<?php $__env->startSection('title', 'المستخدمين'); ?>
<?php $__env->startSection('page-title', 'إدارة المستخدمين'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">المستخدمين</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة المستخدمين</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> مستخدم جديد
                    </a>
                    <a href="<?php echo e(route('roles.index')); ?>" class="btn btn-info">
                        <i class="fas fa-user-tag"></i> إدارة الأدوار
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>القسم / المنصب</th>
                                <th>الأدوار</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <div class="user-avatar">
                                        <?php if($user->avatar): ?>
                                            <img src="<?php echo e(asset('storage/avatars/' . $user->avatar)); ?>" 
                                                 alt="<?php echo e($user->name); ?>" 
                                                 class="img-circle elevation-2" 
                                                 style="width: 40px; height: 40px;">
                                        <?php else: ?>
                                            <div class="bg-primary text-white d-flex align-items-center justify-content-center rounded-circle" 
                                                 style="width: 40px; height: 40px; font-size: 16px; font-weight: bold;">
                                                <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <strong><?php echo e($user->name); ?></strong>
                                    <?php if($user->id === auth()->id()): ?>
                                        <span class="badge badge-warning badge-sm">أنت</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($user->email); ?></td>
                                <td><?php echo e($user->phone ?: '-'); ?></td>
                                <td>
                                    <?php if($user->department): ?>
                                        <strong><?php echo e($user->department); ?></strong>
                                        <?php if($user->position): ?>
                                            <br><small class="text-muted"><?php echo e($user->position); ?></small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php $__empty_2 = true; $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_2 = false; ?>
                                        <span class="badge badge-info"><?php echo e($role->display_name ?? $role->name); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_2): ?>
                                        <span class="badge badge-secondary">بدون دور</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($user->status == 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($user->created_at->format('Y-m-d')); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('users.show', $user->id)); ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('users.edit', $user->id)); ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if($user->id !== auth()->id()): ?>
                                            <button type="button" class="btn btn-<?php echo e($user->status == 'active' ? 'secondary' : 'success'); ?> btn-sm" 
                                                    title="<?php echo e($user->status == 'active' ? 'إلغاء تفعيل' : 'تفعيل'); ?>"
                                                    onclick="confirmAction('<?php echo e(route('users.toggle-status', $user->id)); ?>', 
                                                                          '<?php echo e($user->status == 'active' ? 'إلغاء تفعيل' : 'تفعيل'); ?> المستخدم', 
                                                                          'هل أنت متأكد من <?php echo e($user->status == 'active' ? 'إلغاء تفعيل' : 'تفعيل'); ?> هذا المستخدم؟')">
                                                <i class="fas fa-<?php echo e($user->status == 'active' ? 'user-slash' : 'user-check'); ?>"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                    onclick="confirmDelete('<?php echo e(route('users.destroy', $user->id)); ?>', 'حذف المستخدم', 'هل أنت متأكد من حذف هذا المستخدم؟')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="9" class="text-center">لا توجد مستخدمين</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($users->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($users->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($users->total()); ?></h3>
                <p>إجمالي المستخدمين</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($users->where('status', 'active')->count()); ?></h3>
                <p>المستخدمين النشطين</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-check"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($users->filter(function($user) { return $user->roles->count() > 0; })->count()); ?></h3>
                <p>لديهم أدوار</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-tag"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($users->where('status', 'inactive')->count()); ?></h3>
                <p>غير نشط</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-slash"></i>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmAction(url, title, text) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تأكيد!',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            
            form.appendChild(csrfToken);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

$(document).ready(function() {
    console.log('Users page loaded with notification system');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/users/index.blade.php ENDPATH**/ ?>