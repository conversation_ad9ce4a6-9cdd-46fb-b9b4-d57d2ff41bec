<?php

use App\Helpers\NumberHelper;
use App\Helpers\DateHelper;

if (!function_exists('format_currency')) {
    /**
     * Format currency with Arabic locale
     */
    function format_currency($amount, $currency = 'ريال')
    {
        return NumberHelper::formatCurrency($amount, $currency);
    }
}

if (!function_exists('format_number_arabic')) {
    /**
     * Format number with Arabic locale
     */
    function format_number_arabic($number, $decimals = 2)
    {
        return NumberHelper::formatArabic($number, $decimals);
    }
}

if (!function_exists('calculate_percentage')) {
    /**
     * Calculate percentage
     */
    function calculate_percentage($part, $total)
    {
        return NumberHelper::calculatePercentage($part, $total);
    }
}

if (!function_exists('format_date_arabic')) {
    /**
     * Format date for Arabic display
     */
    function format_date_arabic($date, $format = 'd/m/Y')
    {
        return DateHelper::formatArabic($date, $format);
    }
}

if (!function_exists('time_ago_arabic')) {
    /**
     * Get time ago in Arabic
     */
    function time_ago_arabic($date)
    {
        return DateHelper::timeAgoArabic($date);
    }
}

if (!function_exists('get_arabic_month')) {
    /**
     * Get Arabic month name
     */
    function get_arabic_month($monthNumber)
    {
        return DateHelper::getArabicMonth($monthNumber);
    }
}

if (!function_exists('get_arabic_day')) {
    /**
     * Get Arabic day name
     */
    function get_arabic_day($dayNumber)
    {
        return DateHelper::getArabicDay($dayNumber);
    }
}

if (!function_exists('generate_reference_number')) {
    /**
     * Generate reference number for transactions
     */
    function generate_reference_number($prefix = 'REF', $length = 6)
    {
        $number = str_pad(mt_rand(1, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
        return $prefix . '-' . $number;
    }
}

if (!function_exists('get_status_badge')) {
    /**
     * Get Bootstrap badge class for status
     */
    function get_status_badge($status)
    {
        $badges = [
            'active' => 'badge-success',
            'inactive' => 'badge-secondary',
            'pending' => 'badge-warning',
            'confirmed' => 'badge-success',
            'cancelled' => 'badge-danger',
            'completed' => 'badge-primary',
            'shipped' => 'badge-info',
            'delivered' => 'badge-success',
            'paid' => 'badge-success',
            'unpaid' => 'badge-danger',
            'partial' => 'badge-warning',
            'overdue' => 'badge-danger',
        ];

        return $badges[$status] ?? 'badge-secondary';
    }
}

if (!function_exists('get_status_label_arabic')) {
    /**
     * Get Arabic label for status
     */
    function get_status_label_arabic($status)
    {
        $labels = [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'pending' => 'في الانتظار',
            'confirmed' => 'مؤكد',
            'cancelled' => 'ملغي',
            'completed' => 'مكتمل',
            'shipped' => 'تم الشحن',
            'delivered' => 'تم التسليم',
            'paid' => 'مدفوع',
            'unpaid' => 'غير مدفوع',
            'partial' => 'دفع جزئي',
            'overdue' => 'متأخر',
            'discontinued' => 'متوقف',
            'in_stock' => 'متوفر',
            'low_stock' => 'مخزون منخفض',
            'out_of_stock' => 'نفد المخزون',
            'income' => 'دخل',
            'expense' => 'مصروف',
            'transfer' => 'تحويل',
            'asset' => 'أصل',
            'liability' => 'التزام',
            'equity' => 'حقوق ملكية',
            'revenue' => 'إيراد',
            'cash' => 'نقدي',
            'credit_card' => 'بطاقة ائتمان',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك',
            'other' => 'أخرى',
        ];

        return $labels[$status] ?? $status;
    }
}

if (!function_exists('sanitize_filename')) {
    /**
     * Sanitize filename for Arabic text
     */
    function sanitize_filename($filename)
    {
        // Remove or replace problematic characters
        $filename = preg_replace('/[^\p{L}\p{N}\s\-_.]/u', '', $filename);
        $filename = preg_replace('/\s+/', '_', $filename);
        $filename = trim($filename, '-_.');

        return $filename ?: 'file_' . time();
    }
}

if (!function_exists('get_file_icon')) {
    /**
     * Get file icon based on extension
     */
    function get_file_icon($extension)
    {
        $icons = [
            'pdf' => 'fas fa-file-pdf text-danger',
            'doc' => 'fas fa-file-word text-primary',
            'docx' => 'fas fa-file-word text-primary',
            'xls' => 'fas fa-file-excel text-success',
            'xlsx' => 'fas fa-file-excel text-success',
            'ppt' => 'fas fa-file-powerpoint text-warning',
            'pptx' => 'fas fa-file-powerpoint text-warning',
            'jpg' => 'fas fa-file-image text-info',
            'jpeg' => 'fas fa-file-image text-info',
            'png' => 'fas fa-file-image text-info',
            'gif' => 'fas fa-file-image text-info',
            'zip' => 'fas fa-file-archive text-secondary',
            'rar' => 'fas fa-file-archive text-secondary',
            'txt' => 'fas fa-file-alt text-muted',
        ];

        return $icons[strtolower($extension)] ?? 'fas fa-file text-muted';
    }
}

if (!function_exists('notify_success')) {
    /**
     * Add success notification
     */
    function notify_success($message, $title = null)
    {
        return \App\Helpers\NotificationHelper::success($message, $title);
    }
}

if (!function_exists('notify_error')) {
    /**
     * Add error notification
     */
    function notify_error($message, $title = null)
    {
        return \App\Helpers\NotificationHelper::error($message, $title);
    }
}

if (!function_exists('notify_warning')) {
    /**
     * Add warning notification
     */
    function notify_warning($message, $title = null)
    {
        return \App\Helpers\NotificationHelper::warning($message, $title);
    }
}

if (!function_exists('notify_info')) {
    /**
     * Add info notification
     */
    function notify_info($message, $title = null)
    {
        return \App\Helpers\NotificationHelper::info($message, $title);
    }
}

if (!function_exists('notify_crud')) {
    /**
     * Add CRUD operation notification
     */
    function notify_crud($operation, $entity, $success = true)
    {
        $entityName = \App\Helpers\NotificationHelper::getEntityName($entity);
        return \App\Helpers\NotificationHelper::crud($operation, $entityName, $success);
    }
}
