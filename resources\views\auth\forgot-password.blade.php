@extends('layouts.auth')

@section('title', 'نسيت كلمة المرور')

@section('content')
<div class="card">
    <div class="card-header">
        <div class="login-logo">
            <i class="fas fa-key"></i>
            <strong>OmniFlow</strong> ERP
        </div>
        <p class="text-muted">استعادة كلمة المرور</p>
    </div>

    <div class="card-body">
        <div class="mb-4 text-center text-muted">
            نسيت كلمة المرور؟ لا مشكلة. أدخل بريدك الإلكتروني وسنرسل لك رابط لإعادة تعيين كلمة المرور.
        </div>

        <!-- Session Status -->
        @if (session('status'))
            <div class="alert alert-success" role="alert">
                {{ session('status') }}
            </div>
        @endif

        <form method="POST" action="{{ route('password.email') }}">
            @csrf

            <!-- Email Address -->
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                    </div>
                    <input type="email"
                           class="form-control @error('email') is-invalid @enderror"
                           name="email"
                           value="{{ old('email') }}"
                           placeholder="البريد الإلكتروني"
                           required
                           autofocus>
                </div>
                @error('email')
                    <span class="invalid-feedback d-block">{{ $message }}</span>
                @enderror
            </div>

            <!-- Submit Button -->
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block">
                    <i class="fas fa-paper-plane mr-2"></i>
                    إرسال رابط استعادة كلمة المرور
                </button>
            </div>

            <!-- Links -->
            <div class="text-center">
                <a href="{{ route('login') }}" class="text-link">
                    <i class="fas fa-arrow-left mr-1"></i>
                    العودة لتسجيل الدخول
                </a>
            </div>
        </form>
    </div>
</div>
@endsection
