<?php $__env->startSection('title', 'إعدادات النظام'); ?>
<?php $__env->startSection('page-title', 'إعدادات النظام'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">إعدادات النظام</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card card-primary card-tabs">
            <div class="card-header p-0 pt-1">
                <ul class="nav nav-tabs" id="custom-tabs-one-tab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="company-tab" data-toggle="pill" href="#company" role="tab">
                            <i class="fas fa-building"></i> معلومات الشركة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="system-tab" data-toggle="pill" href="#system" role="tab">
                            <i class="fas fa-cogs"></i> إعدادات النظام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="notifications-tab" data-toggle="pill" href="#notifications" role="tab">
                            <i class="fas fa-bell"></i> الإشعارات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="maintenance-tab" data-toggle="pill" href="#maintenance" role="tab">
                            <i class="fas fa-tools"></i> الصيانة
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('settings.update')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    
                    <div class="tab-content" id="custom-tabs-one-tabContent">
                        <!-- Company Information Tab -->
                        <div class="tab-pane fade show active" id="company" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_name">اسم الشركة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['company_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="company_name" name="company_name" value="<?php echo e(old('company_name', $settings['company_name'])); ?>" required>
                                        <?php $__errorArgs = ['company_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_email">البريد الإلكتروني <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control <?php $__errorArgs = ['company_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="company_email" name="company_email" value="<?php echo e(old('company_email', $settings['company_email'])); ?>" required>
                                        <?php $__errorArgs = ['company_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_phone">رقم الهاتف <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['company_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="company_phone" name="company_phone" value="<?php echo e(old('company_phone', $settings['company_phone'])); ?>" required>
                                        <?php $__errorArgs = ['company_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_tax_number">الرقم الضريبي</label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['company_tax_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="company_tax_number" name="company_tax_number" value="<?php echo e(old('company_tax_number', $settings['company_tax_number'])); ?>">
                                        <?php $__errorArgs = ['company_tax_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_city">المدينة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['company_city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="company_city" name="company_city" value="<?php echo e(old('company_city', $settings['company_city'])); ?>" required>
                                        <?php $__errorArgs = ['company_city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_country">الدولة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['company_country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="company_country" name="company_country" value="<?php echo e(old('company_country', $settings['company_country'])); ?>" required>
                                        <?php $__errorArgs = ['company_country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="company_address">العنوان <span class="text-danger">*</span></label>
                                        <textarea class="form-control <?php $__errorArgs = ['company_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                  id="company_address" name="company_address" rows="3" required><?php echo e(old('company_address', $settings['company_address'])); ?></textarea>
                                        <?php $__errorArgs = ['company_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="company_logo">شعار الشركة</label>
                                        <input type="file" class="form-control-file <?php $__errorArgs = ['company_logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="company_logo" name="company_logo" accept="image/*">
                                        <?php $__errorArgs = ['company_logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <?php if($settings['company_logo']): ?>
                                            <div class="mt-2">
                                                <img src="<?php echo e(asset('storage/' . $settings['company_logo'])); ?>" 
                                                     alt="Company Logo" class="img-thumbnail" style="max-width: 150px;">
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings Tab -->
                        <div class="tab-pane fade" id="system" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="currency">العملة <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="currency" name="currency" required>
                                            <option value="SAR" <?php echo e(old('currency', $settings['currency']) == 'SAR' ? 'selected' : ''); ?>>ريال سعودي (SAR)</option>
                                            <option value="USD" <?php echo e(old('currency', $settings['currency']) == 'USD' ? 'selected' : ''); ?>>دولار أمريكي (USD)</option>
                                            <option value="EUR" <?php echo e(old('currency', $settings['currency']) == 'EUR' ? 'selected' : ''); ?>>يورو (EUR)</option>
                                            <option value="AED" <?php echo e(old('currency', $settings['currency']) == 'AED' ? 'selected' : ''); ?>>درهم إماراتي (AED)</option>
                                        </select>
                                        <?php $__errorArgs = ['currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="timezone">المنطقة الزمنية <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['timezone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="timezone" name="timezone" required>
                                            <option value="Asia/Riyadh" <?php echo e(old('timezone', $settings['timezone']) == 'Asia/Riyadh' ? 'selected' : ''); ?>>الرياض (Asia/Riyadh)</option>
                                            <option value="Asia/Dubai" <?php echo e(old('timezone', $settings['timezone']) == 'Asia/Dubai' ? 'selected' : ''); ?>>دبي (Asia/Dubai)</option>
                                            <option value="Asia/Kuwait" <?php echo e(old('timezone', $settings['timezone']) == 'Asia/Kuwait' ? 'selected' : ''); ?>>الكويت (Asia/Kuwait)</option>
                                            <option value="UTC" <?php echo e(old('timezone', $settings['timezone']) == 'UTC' ? 'selected' : ''); ?>>UTC</option>
                                        </select>
                                        <?php $__errorArgs = ['timezone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="language">اللغة <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['language'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="language" name="language" required>
                                            <option value="ar" <?php echo e(old('language', $settings['language']) == 'ar' ? 'selected' : ''); ?>>العربية</option>
                                            <option value="en" <?php echo e(old('language', $settings['language']) == 'en' ? 'selected' : ''); ?>>English</option>
                                        </select>
                                        <?php $__errorArgs = ['language'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="date_format">تنسيق التاريخ <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['date_format'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="date_format" name="date_format" required>
                                            <option value="Y-m-d" <?php echo e(old('date_format', $settings['date_format']) == 'Y-m-d' ? 'selected' : ''); ?>>YYYY-MM-DD</option>
                                            <option value="d/m/Y" <?php echo e(old('date_format', $settings['date_format']) == 'd/m/Y' ? 'selected' : ''); ?>>DD/MM/YYYY</option>
                                            <option value="m/d/Y" <?php echo e(old('date_format', $settings['date_format']) == 'm/d/Y' ? 'selected' : ''); ?>>MM/DD/YYYY</option>
                                        </select>
                                        <?php $__errorArgs = ['date_format'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="items_per_page">عدد العناصر في الصفحة <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['items_per_page'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="items_per_page" name="items_per_page" required>
                                            <option value="10" <?php echo e(old('items_per_page', $settings['items_per_page']) == 10 ? 'selected' : ''); ?>>10</option>
                                            <option value="15" <?php echo e(old('items_per_page', $settings['items_per_page']) == 15 ? 'selected' : ''); ?>>15</option>
                                            <option value="25" <?php echo e(old('items_per_page', $settings['items_per_page']) == 25 ? 'selected' : ''); ?>>25</option>
                                            <option value="50" <?php echo e(old('items_per_page', $settings['items_per_page']) == 50 ? 'selected' : ''); ?>>50</option>
                                        </select>
                                        <?php $__errorArgs = ['items_per_page'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="low_stock_threshold">حد المخزون المنخفض <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['low_stock_threshold'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="low_stock_threshold" name="low_stock_threshold" 
                                               value="<?php echo e(old('low_stock_threshold', $settings['low_stock_threshold'])); ?>" 
                                               min="1" required>
                                        <?php $__errorArgs = ['low_stock_threshold'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notifications Tab -->
                        <div class="tab-pane fade" id="notifications" role="tabpanel">
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="enable_notifications" name="enable_notifications" value="1"
                                                   <?php echo e(old('enable_notifications', $settings['enable_notifications']) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="enable_notifications">
                                                تفعيل الإشعارات العامة
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="enable_email_notifications" name="enable_email_notifications" value="1"
                                                   <?php echo e(old('enable_email_notifications', $settings['enable_email_notifications']) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="enable_email_notifications">
                                                تفعيل إشعارات البريد الإلكتروني
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="enable_sms_notifications" name="enable_sms_notifications" value="1"
                                                   <?php echo e(old('enable_sms_notifications', $settings['enable_sms_notifications']) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="enable_sms_notifications">
                                                تفعيل إشعارات الرسائل النصية
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Maintenance Tab -->
                        <div class="tab-pane fade" id="maintenance" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="backup_frequency">تكرار النسخ الاحتياطي <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['backup_frequency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="backup_frequency" name="backup_frequency" required>
                                            <option value="daily" <?php echo e(old('backup_frequency', $settings['backup_frequency']) == 'daily' ? 'selected' : ''); ?>>يومي</option>
                                            <option value="weekly" <?php echo e(old('backup_frequency', $settings['backup_frequency']) == 'weekly' ? 'selected' : ''); ?>>أسبوعي</option>
                                            <option value="monthly" <?php echo e(old('backup_frequency', $settings['backup_frequency']) == 'monthly' ? 'selected' : ''); ?>>شهري</option>
                                        </select>
                                        <?php $__errorArgs = ['backup_frequency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check mt-4">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="maintenance_mode" name="maintenance_mode" value="1"
                                                   <?php echo e(old('maintenance_mode', $settings['maintenance_mode']) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="maintenance_mode">
                                                وضع الصيانة
                                            </label>
                                            <small class="form-text text-muted">سيمنع المستخدمين من الوصول للنظام</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="card card-info">
                                        <div class="card-header">
                                            <h3 class="card-title">أدوات الصيانة</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <button type="button" class="btn btn-warning btn-block" onclick="clearCache()">
                                                        <i class="fas fa-broom"></i> مسح الكاش
                                                    </button>
                                                </div>
                                                <div class="col-md-4">
                                                    <a href="<?php echo e(route('settings.export')); ?>" class="btn btn-info btn-block">
                                                        <i class="fas fa-download"></i> تصدير الإعدادات
                                                    </a>
                                                </div>
                                                <div class="col-md-4">
                                                    <button type="button" class="btn btn-secondary btn-block" data-toggle="modal" data-target="#importModal">
                                                        <i class="fas fa-upload"></i> استيراد الإعدادات
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                        <button type="button" class="btn btn-warning" onclick="resetSettings()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                        <a href="<?php echo e(route('settings.system-info')); ?>" class="btn btn-info">
                            <i class="fas fa-info-circle"></i> معلومات النظام
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد الإعدادات</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?php echo e(route('settings.import')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="settings_file">ملف الإعدادات (JSON)</label>
                        <input type="file" class="form-control-file" id="settings_file" name="settings_file" accept=".json" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">استيراد</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function clearCache() {
    Swal.fire({
        title: 'مسح ذاكرة التخزين المؤقت',
        text: 'هل أنت متأكد من مسح جميع ملفات الكاش؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، امسح',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '<?php echo e(route("settings.clear-cache")); ?>';
        }
    });
}

function resetSettings() {
    Swal.fire({
        title: 'إعادة تعيين الإعدادات',
        text: 'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، أعد التعيين',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '<?php echo e(route("settings.reset")); ?>';
        }
    });
}

$(document).ready(function() {
    // Form submission with loading
    $('form').on('submit', function(e) {
        const saveBtn = $('#saveBtn');
        saveBtn.prop('disabled', true);
        saveBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        
        showLoading('جاري حفظ الإعدادات...');
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/settings/index.blade.php ENDPATH**/ ?>