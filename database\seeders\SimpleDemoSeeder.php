<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Sales\Customer;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Product;
use App\Models\Sales\Sale;
use App\Models\Accounting\Account;
use App\Models\Accounting\Transaction;

class SimpleDemoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating simple demo data...');

        // Create demo customers if not exist
        if (Customer::count() == 0) {
            Customer::create([
                'name' => 'أحمد محمد علي',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'address' => 'شارع الملك فهد، الرياض',
                'city' => 'الرياض',
                'country' => 'المملكة العربية السعودية',
                'customer_type' => 'individual',
                'status' => 'active',
                'credit_limit' => 10000,
                'payment_terms' => 30
            ]);

            Customer::create([
                'name' => 'شركة التقنية المتقدمة',
                'email' => '<EMAIL>',
                'phone' => '+966112345678',
                'address' => 'طريق الملك عبدالعزيز، جدة',
                'city' => 'جدة',
                'country' => 'المملكة العربية السعودية',
                'customer_type' => 'company',
                'status' => 'active',
                'credit_limit' => 50000,
                'payment_terms' => 45,
                'tax_number' => '*********'
            ]);

            $this->command->info('✓ Customers created');
        }

        // Create demo suppliers if not exist
        if (Supplier::count() == 0) {
            Supplier::create([
                'name' => 'محمد أحمد التجاري',
                'company_name' => 'مؤسسة محمد أحمد التجارية',
                'email' => '<EMAIL>',
                'phone' => '+966501111111',
                'address' => 'حي العليا، الرياض',
                'city' => 'الرياض',
                'country' => 'المملكة العربية السعودية',
                'status' => 'active',
                'payment_terms' => 30,
                'contact_person' => 'محمد أحمد',
                'contact_phone' => '+966501111111',
                'contact_email' => '<EMAIL>'
            ]);

            $this->command->info('✓ Suppliers created');
        }

        // Create demo products if not exist
        if (Product::count() == 0) {
            Product::create([
                'name' => 'لابتوب Dell Inspiron 15',
                'description' => 'لابتوب Dell Inspiron 15 بمعالج Intel Core i5',
                'sku' => 'DELL-INS-15-001',
                'barcode' => '*********0123',
                'category_id' => 1,
                'supplier_id' => 1,
                'price' => 2500.00,
                'cost_price' => 2000.00,
                'quantity_in_stock' => 25,
                'minimum_stock_level' => 5,
                'status' => 'active',
                'weight' => 2.5,
                'dimensions' => '35x25x2 سم'
            ]);

            Product::create([
                'name' => 'ماوس لاسلكي Logitech',
                'description' => 'ماوس لاسلكي من Logitech بتقنية البلوتوث',
                'sku' => 'LOGI-MOUSE-BT-001',
                'barcode' => '4567890123456',
                'category_id' => 1,
                'supplier_id' => 1,
                'price' => 150.00,
                'cost_price' => 100.00,
                'quantity_in_stock' => 50,
                'minimum_stock_level' => 10,
                'status' => 'active',
                'weight' => 0.1,
                'dimensions' => '10x6x3 سم'
            ]);

            Product::create([
                'name' => 'كيبورد ميكانيكي',
                'description' => 'كيبورد ميكانيكي للألعاب مع إضاءة RGB',
                'sku' => 'MECH-KB-RGB-001',
                'barcode' => '*************',
                'category_id' => 1,
                'supplier_id' => 1,
                'price' => 350.00,
                'cost_price' => 250.00,
                'quantity_in_stock' => 2,
                'minimum_stock_level' => 5,
                'status' => 'active',
                'weight' => 1.2,
                'dimensions' => '45x15x3 سم'
            ]);

            $this->command->info('✓ Products created');
        }

        // Create demo accounts if not exist
        if (Account::count() == 0) {
            Account::create([
                'name' => 'الصندوق',
                'code' => 'AST-CAS-001',
                'type' => 'asset',
                'balance' => 50000.00,
                'currency' => 'SAR',
                'status' => 'active',
                'is_default' => true,
                'description' => 'حساب النقدية في الصندوق'
            ]);

            Account::create([
                'name' => 'البنك الأهلي - الحساب الجاري',
                'code' => 'AST-BNK-001',
                'type' => 'asset',
                'balance' => 150000.00,
                'currency' => 'SAR',
                'status' => 'active',
                'bank_name' => 'البنك الأهلي السعودي',
                'account_number' => '*********0',
                'description' => 'الحساب الجاري في البنك الأهلي'
            ]);

            Account::create([
                'name' => 'مبيعات المنتجات',
                'code' => 'REV-SAL-001',
                'type' => 'revenue',
                'balance' => 0.00,
                'currency' => 'SAR',
                'status' => 'active',
                'is_default' => true,
                'description' => 'إيرادات مبيعات المنتجات'
            ]);

            $this->command->info('✓ Accounts created');
        }

        // Create demo transactions if not exist
        if (Transaction::count() == 0) {
            Transaction::create([
                'account_id' => 1,
                'type' => 'income',
                'amount' => 5000.00,
                'description' => 'إيداع نقدي ابتدائي',
                'transaction_date' => now()->subDays(10),
                'payment_method' => 'cash',
                'status' => 'completed',
                'reference_number' => 'INC-' . date('Ymd') . '-001',
                'user_id' => 1
            ]);

            Transaction::create([
                'account_id' => 2,
                'type' => 'income',
                'amount' => 25000.00,
                'description' => 'تحويل بنكي من العميل',
                'transaction_date' => now()->subDays(8),
                'payment_method' => 'bank_transfer',
                'status' => 'completed',
                'reference_number' => 'INC-' . date('Ymd') . '-002',
                'user_id' => 1
            ]);

            $this->command->info('✓ Transactions created');
        }

        // Create demo sales if not exist
        if (Sale::count() == 0) {
            Sale::create([
                'customer_id' => 1,
                'sale_date' => now()->subDays(3),
                'total_amount' => 3047.50,
                'tax_amount' => 397.50,
                'discount_amount' => 0.00,
                'net_amount' => 2650.00,
                'status' => 'delivered',
                'payment_status' => 'paid',
                'payment_method' => 'cash',
                'notes' => 'بيع لابتوب وماوس للعميل أحمد',
                'reference_number' => 'SAL-' . date('Ymd') . '-001',
                'user_id' => 1
            ]);

            $this->command->info('✓ Sales created');
        }

        $this->command->info('Simple demo data created successfully!');
    }
}
