<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Accounting\Account;
use Illuminate\Http\Request;

class AccountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $accounts = Account::withCount('transactions')
            ->orderBy('type')
            ->orderBy('name')
            ->paginate(15);

        // Group accounts by type for better display
        $accountsByType = $accounts->groupBy('type');

        return view('accounts.index', compact('accounts', 'accountsByType'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('accounts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'nullable|string|max:20|unique:accounts,code',
                'type' => 'required|in:asset,liability,equity,revenue,expense',
                'description' => 'nullable|string|max:1000',
                'balance' => 'nullable|numeric',
                'currency' => 'required|string|max:3',
                'bank_name' => 'nullable|string|max:255',
                'account_number' => 'nullable|string|max:50',
                'routing_number' => 'nullable|string|max:50',
                'status' => 'required|in:active,inactive',
                'is_default' => 'boolean',
                'notes' => 'nullable|string|max:1000'
            ]);

            $data = $request->all();

            // Generate code if not provided
            if (empty($data['code'])) {
                $data['code'] = $this->generateAccountCode($data['name'], $data['type']);
            }

            // Ensure only one default account per type
            if ($request->boolean('is_default')) {
                Account::where('type', $data['type'])
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }

            // Create account
            $account = Account::create($data);

            // Success notification
            notify_crud('created', 'account');

            return redirect()->route('accounts.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'account', false);
            
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Account $account)
    {
        $account->load(['transactions' => function($query) {
            $query->latest('transaction_date')->take(20);
        }]);

        $stats = [
            'current_balance' => $account->getCurrentBalance(),
            'calculated_balance' => $account->getCalculatedBalance(),
            'total_income' => $account->getTotalIncome(),
            'total_expenses' => $account->getTotalExpenses(),
            'transactions_count' => $account->transactions()->count(),
            'last_transaction_date' => $account->transactions()->latest('transaction_date')->first()?->transaction_date
        ];

        return view('accounts.show', compact('account', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Account $account)
    {
        return view('accounts.edit', compact('account'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Account $account)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'nullable|string|max:20|unique:accounts,code,' . $account->id,
                'type' => 'required|in:asset,liability,equity,revenue,expense',
                'description' => 'nullable|string|max:1000',
                'balance' => 'nullable|numeric',
                'currency' => 'required|string|max:3',
                'bank_name' => 'nullable|string|max:255',
                'account_number' => 'nullable|string|max:50',
                'routing_number' => 'nullable|string|max:50',
                'status' => 'required|in:active,inactive',
                'is_default' => 'boolean',
                'notes' => 'nullable|string|max:1000'
            ]);

            $data = $request->all();

            // Ensure only one default account per type
            if ($request->boolean('is_default')) {
                Account::where('type', $data['type'])
                    ->where('id', '!=', $account->id)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }

            // Update account
            $account->update($data);

            // Success notification
            notify_crud('updated', 'account');

            return redirect()->route('accounts.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'account', false);
            
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Account $account)
    {
        try {
            // Check if account has transactions
            if ($account->transactions()->count() > 0) {
                notify_error('لا يمكن حذف الحساب لأنه يحتوي على معاملات مالية', 'خطأ في الحذف');
                return back();
            }

            // Check if it's the default account
            if ($account->is_default) {
                notify_error('لا يمكن حذف الحساب الافتراضي', 'خطأ في الحذف');
                return back();
            }

            $account->delete();

            // Success notification
            notify_crud('deleted', 'account');

            return redirect()->route('accounts.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'account', false);
            
            return back();
        }
    }

    /**
     * Get accounts by type for API.
     */
    public function getByType($type)
    {
        $accounts = Account::where('type', $type)
            ->active()
            ->select('id', 'name', 'code', 'balance')
            ->orderBy('name')
            ->get();

        return response()->json($accounts);
    }

    /**
     * Get account balance.
     */
    public function getBalance(Account $account)
    {
        return response()->json([
            'current_balance' => $account->getCurrentBalance(),
            'calculated_balance' => $account->getCalculatedBalance(),
            'formatted_balance' => $account->formatted_balance
        ]);
    }

    /**
     * Generate account code.
     */
    private function generateAccountCode($name, $type)
    {
        $typePrefix = [
            'asset' => 'AST',
            'liability' => 'LIB',
            'equity' => 'EQT',
            'revenue' => 'REV',
            'expense' => 'EXP'
        ];

        $prefix = $typePrefix[$type] ?? 'ACC';
        $nameCode = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $name), 0, 3));
        $number = str_pad(Account::where('type', $type)->count() + 1, 3, '0', STR_PAD_LEFT);

        return $prefix . '-' . $nameCode . '-' . $number;
    }

    /**
     * Get account statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_accounts' => Account::count(),
            'active_accounts' => Account::active()->count(),
            'total_assets' => Account::assets()->sum('balance'),
            'total_liabilities' => Account::liabilities()->sum('balance'),
            'total_equity' => Account::equity()->sum('balance'),
            'total_revenue' => Account::revenue()->sum('balance'),
            'total_expenses' => Account::expense()->sum('balance'),
            'bank_accounts' => Account::whereNotNull('account_number')->count()
        ];

        return response()->json($stats);
    }
}
