@extends('layouts.app')

@section('title', 'المستخدمين')
@section('page-title', 'إدارة المستخدمين')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">المستخدمين</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة المستخدمين</h3>
                <div class="card-tools">
                    <a href="{{ route('users.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> مستخدم جديد
                    </a>
                    <a href="{{ route('roles.index') }}" class="btn btn-info">
                        <i class="fas fa-user-tag"></i> إدارة الأدوار
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>القسم / المنصب</th>
                                <th>الأدوار</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($users as $user)
                            <tr>
                                <td>
                                    <div class="user-avatar">
                                        @if($user->avatar)
                                            <img src="{{ asset('storage/avatars/' . $user->avatar) }}" 
                                                 alt="{{ $user->name }}" 
                                                 class="img-circle elevation-2" 
                                                 style="width: 40px; height: 40px;">
                                        @else
                                            <div class="bg-primary text-white d-flex align-items-center justify-content-center rounded-circle" 
                                                 style="width: 40px; height: 40px; font-size: 16px; font-weight: bold;">
                                                {{ strtoupper(substr($user->name, 0, 1)) }}
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <strong>{{ $user->name }}</strong>
                                    @if($user->id === auth()->id())
                                        <span class="badge badge-warning badge-sm">أنت</span>
                                    @endif
                                </td>
                                <td>{{ $user->email }}</td>
                                <td>{{ $user->phone ?: '-' }}</td>
                                <td>
                                    @if($user->department)
                                        <strong>{{ $user->department }}</strong>
                                        @if($user->position)
                                            <br><small class="text-muted">{{ $user->position }}</small>
                                        @endif
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>
                                    @forelse($user->roles as $role)
                                        <span class="badge badge-info">{{ $role->display_name ?? $role->name }}</span>
                                    @empty
                                        <span class="badge badge-secondary">بدون دور</span>
                                    @endforelse
                                </td>
                                <td>
                                    @if($user->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @else
                                        <span class="badge badge-danger">غير نشط</span>
                                    @endif
                                </td>
                                <td>{{ $user->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('users.show', $user->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('users.edit', $user->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if($user->id !== auth()->id())
                                            <button type="button" class="btn btn-{{ $user->status == 'active' ? 'secondary' : 'success' }} btn-sm" 
                                                    title="{{ $user->status == 'active' ? 'إلغاء تفعيل' : 'تفعيل' }}"
                                                    onclick="confirmAction('{{ route('users.toggle-status', $user->id) }}', 
                                                                          '{{ $user->status == 'active' ? 'إلغاء تفعيل' : 'تفعيل' }} المستخدم', 
                                                                          'هل أنت متأكد من {{ $user->status == 'active' ? 'إلغاء تفعيل' : 'تفعيل' }} هذا المستخدم؟')">
                                                <i class="fas fa-{{ $user->status == 'active' ? 'user-slash' : 'user-check' }}"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                    onclick="confirmDelete('{{ route('users.destroy', $user->id) }}', 'حذف المستخدم', 'هل أنت متأكد من حذف هذا المستخدم؟')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center">لا توجد مستخدمين</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($users->hasPages())
            <div class="card-footer">
                {{ $users->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $users->total() }}</h3>
                <p>إجمالي المستخدمين</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $users->where('status', 'active')->count() }}</h3>
                <p>المستخدمين النشطين</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-check"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $users->filter(function($user) { return $user->roles->count() > 0; })->count() }}</h3>
                <p>لديهم أدوار</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-tag"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $users->where('status', 'inactive')->count() }}</h3>
                <p>غير نشط</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-slash"></i>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmAction(url, title, text) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تأكيد!',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            
            form.appendChild(csrfToken);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

$(document).ready(function() {
    console.log('Users page loaded with notification system');
});
</script>
@endpush
