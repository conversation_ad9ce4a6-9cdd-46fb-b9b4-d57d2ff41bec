@extends('layouts.app')

@section('title', 'عرض المنتج: ' . $product->name)
@section('page-title', 'عرض المنتج')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="{{ route('products.index') }}">المنتجات</a></li>
    <li class="breadcrumb-item active">{{ $product->name }}</li>
@endsection

@section('content')
<div class="row">
    <!-- Product Information -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">معلومات المنتج</h3>
                <div class="card-tools">
                    <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    <a href="{{ route('products.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> العودة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        @if($product->image)
                            <img src="{{ asset('storage/' . $product->image) }}" 
                                 alt="{{ $product->name }}" 
                                 class="img-fluid rounded">
                        @else
                            <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-8">
                        <h4>{{ $product->name }}</h4>
                        @if($product->description)
                            <p class="text-muted">{{ $product->description }}</p>
                        @endif
                        
                        <table class="table table-sm">
                            <tr>
                                <th width="30%">رمز المنتج (SKU):</th>
                                <td><code>{{ $product->sku }}</code></td>
                            </tr>
                            @if($product->barcode)
                            <tr>
                                <th>الباركود:</th>
                                <td><code>{{ $product->barcode }}</code></td>
                            </tr>
                            @endif
                            <tr>
                                <th>التصنيف:</th>
                                <td>{{ $product->category->name ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>المورد:</th>
                                <td>{{ $product->supplier->name ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>الحالة:</th>
                                <td>
                                    @if($product->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @else
                                        <span class="badge badge-danger">غير نشط</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Details -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">تفاصيل إضافية</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            @if($product->weight)
                            <tr>
                                <th>الوزن:</th>
                                <td>{{ $product->weight }} كجم</td>
                            </tr>
                            @endif
                            @if($product->dimensions)
                            <tr>
                                <th>الأبعاد:</th>
                                <td>{{ $product->dimensions }}</td>
                            </tr>
                            @endif
                            <tr>
                                <th>تاريخ الإنشاء:</th>
                                <td>{{ $product->created_at->format('Y-m-d H:i') }}</td>
                            </tr>
                            <tr>
                                <th>آخر تحديث:</th>
                                <td>{{ $product->updated_at->format('Y-m-d H:i') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <!-- Additional information can be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Pricing Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">معلومات التسعير</h3>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <th>سعر التكلفة:</th>
                        <td class="text-right">{{ number_format($product->cost_price, 2) }} ر.س</td>
                    </tr>
                    <tr>
                        <th>سعر البيع:</th>
                        <td class="text-right"><strong>{{ number_format($product->price, 2) }} ر.س</strong></td>
                    </tr>
                    <tr>
                        <th>هامش الربح:</th>
                        <td class="text-right">
                            @php
                                $profit = $product->price - $product->cost_price;
                                $margin = $product->price > 0 ? ($profit / $product->price) * 100 : 0;
                            @endphp
                            <span class="badge badge-{{ $margin > 20 ? 'success' : ($margin > 10 ? 'warning' : 'danger') }}">
                                {{ number_format($margin, 1) }}%
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>الربح المتوقع:</th>
                        <td class="text-right">{{ number_format($profit, 2) }} ر.س</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Stock Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">معلومات المخزون</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#stockModal">
                        <i class="fas fa-edit"></i> تحديث
                    </button>
                </div>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <th>الكمية الحالية:</th>
                        <td class="text-right">
                            <span class="badge badge-{{ $product->quantity_in_stock <= $product->minimum_stock_level ? 'danger' : 'success' }} badge-lg">
                                {{ $product->quantity_in_stock }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>الحد الأدنى:</th>
                        <td class="text-right">{{ $product->minimum_stock_level }}</td>
                    </tr>
                    <tr>
                        <th>حالة المخزون:</th>
                        <td class="text-right">
                            @if($product->quantity_in_stock == 0)
                                <span class="badge badge-danger">نفد المخزون</span>
                            @elseif($product->quantity_in_stock <= $product->minimum_stock_level)
                                <span class="badge badge-warning">مخزون منخفض</span>
                            @else
                                <span class="badge badge-success">متوفر</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>القيمة الإجمالية:</th>
                        <td class="text-right">
                            <strong>{{ number_format($product->quantity_in_stock * $product->cost_price, 2) }} ر.س</strong>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">إجراءات سريعة</h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning btn-block">
                        <i class="fas fa-edit"></i> تعديل المنتج
                    </a>
                    <button type="button" class="btn btn-success btn-block" data-toggle="modal" data-target="#stockModal">
                        <i class="fas fa-boxes"></i> تحديث المخزون
                    </button>
                    <button type="button" class="btn btn-danger btn-block" 
                            onclick="confirmDelete('{{ route('products.destroy', $product->id) }}', 'حذف المنتج', 'هل أنت متأكد من حذف هذا المنتج؟')">
                        <i class="fas fa-trash"></i> حذف المنتج
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stock Update Modal -->
<div class="modal fade" id="stockModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث مخزون: {{ $product->name }}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('products.update-stock', $product->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>المخزون الحالي</label>
                        <input type="text" class="form-control" value="{{ $product->quantity_in_stock }}" readonly>
                    </div>
                    <div class="form-group">
                        <label for="type">نوع العملية</label>
                        <select class="form-control" name="type" required>
                            <option value="add">إضافة</option>
                            <option value="subtract">خصم</option>
                            <option value="set">تعيين</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">الكمية</label>
                        <input type="number" class="form-control" name="quantity" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="reason">السبب</label>
                        <input type="text" class="form-control" name="reason" placeholder="سبب التحديث (اختياري)">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    console.log('Product details page loaded');
});
</script>
@endpush
