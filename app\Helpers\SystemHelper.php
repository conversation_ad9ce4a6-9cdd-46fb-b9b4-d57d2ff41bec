<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SystemHelper
{
    /**
     * Get system settings.
     */
    public static function getSettings()
    {
        return Cache::get('system_settings', []);
    }

    /**
     * Get single setting.
     */
    public static function getSetting($key, $default = null)
    {
        $settings = self::getSettings();
        return $settings[$key] ?? $default;
    }

    /**
     * Get company name.
     */
    public static function getCompanyName()
    {
        return self::getSetting('company_name', 'OmniFlow ERP');
    }

    /**
     * Get company logo.
     */
    public static function getCompanyLogo()
    {
        $logo = self::getSetting('company_logo');
        return $logo ? asset('storage/' . $logo) : asset('dist/img/AdminLTELogo.png');
    }

    /**
     * Get currency symbol.
     */
    public static function getCurrencySymbol()
    {
        $currency = self::getSetting('currency', 'SAR');
        
        $symbols = [
            'SAR' => 'ر.س',
            'USD' => '$',
            'EUR' => '€',
            'AED' => 'د.إ',
        ];

        return $symbols[$currency] ?? $currency;
    }

    /**
     * Format currency.
     */
    public static function formatCurrency($amount)
    {
        $symbol = self::getCurrencySymbol();
        return $symbol . ' ' . number_format($amount, 2);
    }

    /**
     * Get system statistics.
     */
    public static function getSystemStats()
    {
        return Cache::remember('system_stats', 3600, function () {
            return [
                'total_sales' => DB::table('sales')->count(),
                'total_customers' => DB::table('customers')->count(),
                'total_products' => DB::table('products')->count(),
                'total_users' => DB::table('users')->count(),
                'total_transactions' => DB::table('transactions')->count(),
                'low_stock_products' => DB::table('products')
                    ->whereRaw('quantity <= minimum_stock')
                    ->count(),
            ];
        });
    }

    /**
     * Check if maintenance mode is enabled.
     */
    public static function isMaintenanceMode()
    {
        return self::getSetting('maintenance_mode', false);
    }

    /**
     * Get low stock threshold.
     */
    public static function getLowStockThreshold()
    {
        return self::getSetting('low_stock_threshold', 10);
    }

    /**
     * Get items per page.
     */
    public static function getItemsPerPage()
    {
        return self::getSetting('items_per_page', 15);
    }

    /**
     * Get date format.
     */
    public static function getDateFormat()
    {
        return self::getSetting('date_format', 'Y-m-d');
    }

    /**
     * Format date according to system settings.
     */
    public static function formatDate($date)
    {
        if (!$date) return '';
        
        $format = self::getDateFormat();
        return $date instanceof \Carbon\Carbon ? $date->format($format) : date($format, strtotime($date));
    }

    /**
     * Get system language.
     */
    public static function getLanguage()
    {
        return self::getSetting('language', 'ar');
    }

    /**
     * Check if notifications are enabled.
     */
    public static function areNotificationsEnabled()
    {
        return self::getSetting('enable_notifications', true);
    }

    /**
     * Check if email notifications are enabled.
     */
    public static function areEmailNotificationsEnabled()
    {
        return self::getSetting('enable_email_notifications', true);
    }

    /**
     * Get backup frequency.
     */
    public static function getBackupFrequency()
    {
        return self::getSetting('backup_frequency', 'weekly');
    }

    /**
     * Get system version.
     */
    public static function getSystemVersion()
    {
        return '1.0.0';
    }

    /**
     * Get system name.
     */
    public static function getSystemName()
    {
        return 'OmniFlow ERP 2025';
    }

    /**
     * Check system health.
     */
    public static function checkSystemHealth()
    {
        $health = [
            'database' => false,
            'storage' => false,
            'cache' => false,
        ];

        try {
            // Check database connection
            DB::connection()->getPdo();
            $health['database'] = true;
        } catch (\Exception $e) {
            $health['database'] = false;
        }

        try {
            // Check storage
            $health['storage'] = is_writable(storage_path());
        } catch (\Exception $e) {
            $health['storage'] = false;
        }

        try {
            // Check cache
            Cache::put('health_check', 'ok', 60);
            $health['cache'] = Cache::get('health_check') === 'ok';
        } catch (\Exception $e) {
            $health['cache'] = false;
        }

        return $health;
    }

    /**
     * Get system requirements status.
     */
    public static function checkSystemRequirements()
    {
        return [
            'php_version' => version_compare(PHP_VERSION, '8.1.0', '>='),
            'mysql_extension' => extension_loaded('pdo_mysql'),
            'openssl_extension' => extension_loaded('openssl'),
            'mbstring_extension' => extension_loaded('mbstring'),
            'tokenizer_extension' => extension_loaded('tokenizer'),
            'xml_extension' => extension_loaded('xml'),
            'ctype_extension' => extension_loaded('ctype'),
            'json_extension' => extension_loaded('json'),
            'bcmath_extension' => extension_loaded('bcmath'),
            'fileinfo_extension' => extension_loaded('fileinfo'),
            'storage_writable' => is_writable(storage_path()),
            'bootstrap_cache_writable' => is_writable(storage_path('framework')),
        ];
    }

    /**
     * Clear all system caches.
     */
    public static function clearAllCaches()
    {
        try {
            Cache::flush();
            
            // Clear Laravel caches
            \Artisan::call('config:clear');
            \Artisan::call('route:clear');
            \Artisan::call('view:clear');
            \Artisan::call('cache:clear');
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get disk usage information.
     */
    public static function getDiskUsage()
    {
        $path = base_path();
        
        return [
            'free' => disk_free_space($path),
            'total' => disk_total_space($path),
            'used' => disk_total_space($path) - disk_free_space($path),
        ];
    }

    /**
     * Format bytes to human readable format.
     */
    public static function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get memory usage.
     */
    public static function getMemoryUsage()
    {
        return [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit'),
        ];
    }
}
