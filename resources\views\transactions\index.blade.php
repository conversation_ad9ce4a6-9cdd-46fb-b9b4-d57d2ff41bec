@extends('layouts.app')

@section('title', 'المعاملات المالية')
@section('page-title', 'إدارة المعاملات المالية')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="{{ route('accounting.index') }}">المحاسبة</a></li>
    <li class="breadcrumb-item active">المعاملات</li>
@endsection

@section('content')
<!-- Filters -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">فلترة المعاملات</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('transactions.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="type">نوع المعاملة</label>
                                <select class="form-control" id="type" name="type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="income" {{ request('type') == 'income' ? 'selected' : '' }}>دخل</option>
                                    <option value="expense" {{ request('type') == 'expense' ? 'selected' : '' }}>مصروف</option>
                                    <option value="transfer" {{ request('type') == 'transfer' ? 'selected' : '' }}>تحويل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="account_id">الحساب</label>
                                <select class="form-control" id="account_id" name="account_id">
                                    <option value="">جميع الحسابات</option>
                                    @foreach($accounts as $account)
                                        <option value="{{ $account->id }}" {{ request('account_id') == $account->id ? 'selected' : '' }}>
                                            {{ $account->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="date_from">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="date_to">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="{{ route('transactions.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transactions List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة المعاملات المالية</h3>
                <div class="card-tools">
                    <a href="{{ route('transactions.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> معاملة جديدة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>رقم المرجع</th>
                                <th>التاريخ</th>
                                <th>الحساب</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>التسوية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($transactions as $transaction)
                            <tr>
                                <td>
                                    <code>{{ $transaction->reference_number }}</code>
                                    @if($transaction->invoice_number)
                                        <br><small class="text-muted">فاتورة: {{ $transaction->invoice_number }}</small>
                                    @endif
                                </td>
                                <td>{{ $transaction->transaction_date->format('Y-m-d') }}</td>
                                <td>
                                    <strong>{{ $transaction->account->name }}</strong>
                                    <br><small class="text-muted">{{ $transaction->account->code }}</small>
                                </td>
                                <td>
                                    @switch($transaction->type)
                                        @case('income')
                                            <span class="badge badge-success">دخل</span>
                                            @break
                                        @case('expense')
                                            <span class="badge badge-danger">مصروف</span>
                                            @break
                                        @case('transfer')
                                            <span class="badge badge-info">تحويل</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $transaction->type }}</span>
                                    @endswitch
                                </td>
                                <td>
                                    <strong class="{{ $transaction->type == 'income' ? 'text-success' : 'text-danger' }}">
                                        {{ $transaction->type == 'income' ? '+' : '-' }}{{ number_format($transaction->amount, 2) }}
                                    </strong>
                                </td>
                                <td>{{ Str::limit($transaction->description, 30) }}</td>
                                <td>
                                    @switch($transaction->payment_method)
                                        @case('cash')
                                            <span class="badge badge-success">نقدي</span>
                                            @break
                                        @case('credit_card')
                                            <span class="badge badge-primary">بطاقة ائتمان</span>
                                            @break
                                        @case('bank_transfer')
                                            <span class="badge badge-info">تحويل بنكي</span>
                                            @break
                                        @case('check')
                                            <span class="badge badge-warning">شيك</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">أخرى</span>
                                    @endswitch
                                </td>
                                <td>
                                    @switch($transaction->status)
                                        @case('completed')
                                            <span class="badge badge-success">مكتملة</span>
                                            @break
                                        @case('pending')
                                            <span class="badge badge-warning">في الانتظار</span>
                                            @break
                                        @case('cancelled')
                                            <span class="badge badge-danger">ملغية</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $transaction->status }}</span>
                                    @endswitch
                                </td>
                                <td>
                                    @if($transaction->isReconciled())
                                        <span class="badge badge-success">مسوّاة</span>
                                        <br><small class="text-muted">{{ $transaction->reconciled_at->format('Y-m-d') }}</small>
                                    @else
                                        <span class="badge badge-warning">غير مسوّاة</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('transactions.show', $transaction->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('transactions.edit', $transaction->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if($transaction->isReconciled())
                                            <button type="button" class="btn btn-secondary btn-sm" title="إلغاء التسوية"
                                                    onclick="confirmAction('{{ route('transactions.unreconcile', $transaction->id) }}', 'إلغاء تسوية المعاملة', 'هل أنت متأكد من إلغاء تسوية هذه المعاملة؟')">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        @else
                                            <button type="button" class="btn btn-success btn-sm" title="تسوية"
                                                    onclick="confirmAction('{{ route('transactions.reconcile', $transaction->id) }}', 'تسوية المعاملة', 'هل أنت متأكد من تسوية هذه المعاملة؟')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        @endif
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('{{ route('transactions.destroy', $transaction->id) }}', 'حذف المعاملة', 'هل أنت متأكد من حذف هذه المعاملة؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center">لا توجد معاملات</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($transactions->hasPages())
            <div class="card-footer">
                {{ $transactions->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $transactions->where('type', 'income')->where('status', 'completed')->sum('amount') }}</h3>
                <p>إجمالي الدخل</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-up"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $transactions->where('type', 'expense')->where('status', 'completed')->sum('amount') }}</h3>
                <p>إجمالي المصروفات</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-down"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $transactions->where('status', 'completed')->count() }}</h3>
                <p>المعاملات المكتملة</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $transactions->whereNotNull('reconciled_at')->count() }}</h3>
                <p>المعاملات المسوّاة</p>
            </div>
            <div class="icon">
                <i class="fas fa-balance-scale"></i>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmAction(url, title, text) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تأكيد!',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            
            form.appendChild(csrfToken);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

$(document).ready(function() {
    console.log('Transactions page loaded with notification system');
});
</script>
@endpush
