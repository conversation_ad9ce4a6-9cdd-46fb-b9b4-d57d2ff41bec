@extends('layouts.app')

@section('title', 'إعدادات النظام')
@section('page-title', 'إعدادات النظام')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">إعدادات النظام</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card card-primary card-tabs">
            <div class="card-header p-0 pt-1">
                <ul class="nav nav-tabs" id="custom-tabs-one-tab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="company-tab" data-toggle="pill" href="#company" role="tab">
                            <i class="fas fa-building"></i> معلومات الشركة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="system-tab" data-toggle="pill" href="#system" role="tab">
                            <i class="fas fa-cogs"></i> إعدادات النظام
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="notifications-tab" data-toggle="pill" href="#notifications" role="tab">
                            <i class="fas fa-bell"></i> الإشعارات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="maintenance-tab" data-toggle="pill" href="#maintenance" role="tab">
                            <i class="fas fa-tools"></i> الصيانة
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <form action="{{ route('settings.update') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="tab-content" id="custom-tabs-one-tabContent">
                        <!-- Company Information Tab -->
                        <div class="tab-pane fade show active" id="company" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_name">اسم الشركة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('company_name') is-invalid @enderror" 
                                               id="company_name" name="company_name" value="{{ old('company_name', $settings['company_name']) }}" required>
                                        @error('company_name')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_email">البريد الإلكتروني <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control @error('company_email') is-invalid @enderror" 
                                               id="company_email" name="company_email" value="{{ old('company_email', $settings['company_email']) }}" required>
                                        @error('company_email')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_phone">رقم الهاتف <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('company_phone') is-invalid @enderror" 
                                               id="company_phone" name="company_phone" value="{{ old('company_phone', $settings['company_phone']) }}" required>
                                        @error('company_phone')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_tax_number">الرقم الضريبي</label>
                                        <input type="text" class="form-control @error('company_tax_number') is-invalid @enderror" 
                                               id="company_tax_number" name="company_tax_number" value="{{ old('company_tax_number', $settings['company_tax_number']) }}">
                                        @error('company_tax_number')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_city">المدينة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('company_city') is-invalid @enderror" 
                                               id="company_city" name="company_city" value="{{ old('company_city', $settings['company_city']) }}" required>
                                        @error('company_city')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="company_country">الدولة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('company_country') is-invalid @enderror" 
                                               id="company_country" name="company_country" value="{{ old('company_country', $settings['company_country']) }}" required>
                                        @error('company_country')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="company_address">العنوان <span class="text-danger">*</span></label>
                                        <textarea class="form-control @error('company_address') is-invalid @enderror" 
                                                  id="company_address" name="company_address" rows="3" required>{{ old('company_address', $settings['company_address']) }}</textarea>
                                        @error('company_address')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="company_logo">شعار الشركة</label>
                                        <input type="file" class="form-control-file @error('company_logo') is-invalid @enderror" 
                                               id="company_logo" name="company_logo" accept="image/*">
                                        @error('company_logo')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                        @if($settings['company_logo'])
                                            <div class="mt-2">
                                                <img src="{{ asset('storage/' . $settings['company_logo']) }}" 
                                                     alt="Company Logo" class="img-thumbnail" style="max-width: 150px;">
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings Tab -->
                        <div class="tab-pane fade" id="system" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="currency">العملة <span class="text-danger">*</span></label>
                                        <select class="form-control @error('currency') is-invalid @enderror" 
                                                id="currency" name="currency" required>
                                            <option value="SAR" {{ old('currency', $settings['currency']) == 'SAR' ? 'selected' : '' }}>ريال سعودي (SAR)</option>
                                            <option value="USD" {{ old('currency', $settings['currency']) == 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                                            <option value="EUR" {{ old('currency', $settings['currency']) == 'EUR' ? 'selected' : '' }}>يورو (EUR)</option>
                                            <option value="AED" {{ old('currency', $settings['currency']) == 'AED' ? 'selected' : '' }}>درهم إماراتي (AED)</option>
                                        </select>
                                        @error('currency')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="timezone">المنطقة الزمنية <span class="text-danger">*</span></label>
                                        <select class="form-control @error('timezone') is-invalid @enderror" 
                                                id="timezone" name="timezone" required>
                                            <option value="Asia/Riyadh" {{ old('timezone', $settings['timezone']) == 'Asia/Riyadh' ? 'selected' : '' }}>الرياض (Asia/Riyadh)</option>
                                            <option value="Asia/Dubai" {{ old('timezone', $settings['timezone']) == 'Asia/Dubai' ? 'selected' : '' }}>دبي (Asia/Dubai)</option>
                                            <option value="Asia/Kuwait" {{ old('timezone', $settings['timezone']) == 'Asia/Kuwait' ? 'selected' : '' }}>الكويت (Asia/Kuwait)</option>
                                            <option value="UTC" {{ old('timezone', $settings['timezone']) == 'UTC' ? 'selected' : '' }}>UTC</option>
                                        </select>
                                        @error('timezone')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="language">اللغة <span class="text-danger">*</span></label>
                                        <select class="form-control @error('language') is-invalid @enderror" 
                                                id="language" name="language" required>
                                            <option value="ar" {{ old('language', $settings['language']) == 'ar' ? 'selected' : '' }}>العربية</option>
                                            <option value="en" {{ old('language', $settings['language']) == 'en' ? 'selected' : '' }}>English</option>
                                        </select>
                                        @error('language')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="date_format">تنسيق التاريخ <span class="text-danger">*</span></label>
                                        <select class="form-control @error('date_format') is-invalid @enderror" 
                                                id="date_format" name="date_format" required>
                                            <option value="Y-m-d" {{ old('date_format', $settings['date_format']) == 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                                            <option value="d/m/Y" {{ old('date_format', $settings['date_format']) == 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                                            <option value="m/d/Y" {{ old('date_format', $settings['date_format']) == 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                                        </select>
                                        @error('date_format')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="items_per_page">عدد العناصر في الصفحة <span class="text-danger">*</span></label>
                                        <select class="form-control @error('items_per_page') is-invalid @enderror" 
                                                id="items_per_page" name="items_per_page" required>
                                            <option value="10" {{ old('items_per_page', $settings['items_per_page']) == 10 ? 'selected' : '' }}>10</option>
                                            <option value="15" {{ old('items_per_page', $settings['items_per_page']) == 15 ? 'selected' : '' }}>15</option>
                                            <option value="25" {{ old('items_per_page', $settings['items_per_page']) == 25 ? 'selected' : '' }}>25</option>
                                            <option value="50" {{ old('items_per_page', $settings['items_per_page']) == 50 ? 'selected' : '' }}>50</option>
                                        </select>
                                        @error('items_per_page')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="low_stock_threshold">حد المخزون المنخفض <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control @error('low_stock_threshold') is-invalid @enderror" 
                                               id="low_stock_threshold" name="low_stock_threshold" 
                                               value="{{ old('low_stock_threshold', $settings['low_stock_threshold']) }}" 
                                               min="1" required>
                                        @error('low_stock_threshold')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notifications Tab -->
                        <div class="tab-pane fade" id="notifications" role="tabpanel">
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="enable_notifications" name="enable_notifications" value="1"
                                                   {{ old('enable_notifications', $settings['enable_notifications']) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="enable_notifications">
                                                تفعيل الإشعارات العامة
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="enable_email_notifications" name="enable_email_notifications" value="1"
                                                   {{ old('enable_email_notifications', $settings['enable_email_notifications']) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="enable_email_notifications">
                                                تفعيل إشعارات البريد الإلكتروني
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="enable_sms_notifications" name="enable_sms_notifications" value="1"
                                                   {{ old('enable_sms_notifications', $settings['enable_sms_notifications']) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="enable_sms_notifications">
                                                تفعيل إشعارات الرسائل النصية
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Maintenance Tab -->
                        <div class="tab-pane fade" id="maintenance" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="backup_frequency">تكرار النسخ الاحتياطي <span class="text-danger">*</span></label>
                                        <select class="form-control @error('backup_frequency') is-invalid @enderror" 
                                                id="backup_frequency" name="backup_frequency" required>
                                            <option value="daily" {{ old('backup_frequency', $settings['backup_frequency']) == 'daily' ? 'selected' : '' }}>يومي</option>
                                            <option value="weekly" {{ old('backup_frequency', $settings['backup_frequency']) == 'weekly' ? 'selected' : '' }}>أسبوعي</option>
                                            <option value="monthly" {{ old('backup_frequency', $settings['backup_frequency']) == 'monthly' ? 'selected' : '' }}>شهري</option>
                                        </select>
                                        @error('backup_frequency')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check mt-4">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="maintenance_mode" name="maintenance_mode" value="1"
                                                   {{ old('maintenance_mode', $settings['maintenance_mode']) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="maintenance_mode">
                                                وضع الصيانة
                                            </label>
                                            <small class="form-text text-muted">سيمنع المستخدمين من الوصول للنظام</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="card card-info">
                                        <div class="card-header">
                                            <h3 class="card-title">أدوات الصيانة</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <button type="button" class="btn btn-warning btn-block" onclick="clearCache()">
                                                        <i class="fas fa-broom"></i> مسح الكاش
                                                    </button>
                                                </div>
                                                <div class="col-md-4">
                                                    <a href="{{ route('settings.export') }}" class="btn btn-info btn-block">
                                                        <i class="fas fa-download"></i> تصدير الإعدادات
                                                    </a>
                                                </div>
                                                <div class="col-md-4">
                                                    <button type="button" class="btn btn-secondary btn-block" data-toggle="modal" data-target="#importModal">
                                                        <i class="fas fa-upload"></i> استيراد الإعدادات
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                        <button type="button" class="btn btn-warning" onclick="resetSettings()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                        <a href="{{ route('settings.system-info') }}" class="btn btn-info">
                            <i class="fas fa-info-circle"></i> معلومات النظام
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد الإعدادات</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('settings.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="settings_file">ملف الإعدادات (JSON)</label>
                        <input type="file" class="form-control-file" id="settings_file" name="settings_file" accept=".json" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">استيراد</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function clearCache() {
    Swal.fire({
        title: 'مسح ذاكرة التخزين المؤقت',
        text: 'هل أنت متأكد من مسح جميع ملفات الكاش؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، امسح',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '{{ route("settings.clear-cache") }}';
        }
    });
}

function resetSettings() {
    Swal.fire({
        title: 'إعادة تعيين الإعدادات',
        text: 'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، أعد التعيين',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '{{ route("settings.reset") }}';
        }
    });
}

$(document).ready(function() {
    // Form submission with loading
    $('form').on('submit', function(e) {
        const saveBtn = $('#saveBtn');
        saveBtn.prop('disabled', true);
        saveBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        
        showLoading('جاري حفظ الإعدادات...');
    });
});
</script>
@endpush
