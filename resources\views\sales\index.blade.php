@extends('layouts.app')

@section('title', 'المبيعات')
@section('page-title', 'إدارة المبيعات')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">المبيعات</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة المبيعات</h3>
                <div class="card-tools">
                    <a href="{{ route('sales.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> مبيعة جديدة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الرقم المرجعي</th>
                                <th>العميل</th>
                                <th>تاريخ البيع</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المبلغ الصافي</th>
                                <th>حالة البيع</th>
                                <th>حالة الدفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($sales as $sale)
                            <tr>
                                <td>{{ $sale->reference_number }}</td>
                                <td>{{ $sale->customer->name }}</td>
                                <td>{{ $sale->sale_date->format('d/m/Y') }}</td>
                                <td>{{ number_format($sale->total_amount, 2) }} ريال</td>
                                <td>{{ number_format($sale->net_amount, 2) }} ريال</td>
                                <td>
                                    @if($sale->status == 'pending')
                                        <span class="badge badge-warning">في الانتظار</span>
                                    @elseif($sale->status == 'confirmed')
                                        <span class="badge badge-success">مؤكد</span>
                                    @elseif($sale->status == 'shipped')
                                        <span class="badge badge-info">تم الشحن</span>
                                    @elseif($sale->status == 'delivered')
                                        <span class="badge badge-primary">تم التسليم</span>
                                    @elseif($sale->status == 'cancelled')
                                        <span class="badge badge-danger">ملغي</span>
                                    @else
                                        <span class="badge badge-secondary">{{ $sale->status }}</span>
                                    @endif
                                </td>
                                <td>
                                    @if($sale->payment_status == 'pending')
                                        <span class="badge badge-warning">في الانتظار</span>
                                    @elseif($sale->payment_status == 'partial')
                                        <span class="badge badge-info">دفع جزئي</span>
                                    @elseif($sale->payment_status == 'paid')
                                        <span class="badge badge-success">مدفوع</span>
                                    @elseif($sale->payment_status == 'overdue')
                                        <span class="badge badge-danger">متأخر</span>
                                    @else
                                        <span class="badge badge-secondary">{{ $sale->payment_status }}</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('sales.show', $sale->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('sales.edit', $sale->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف"
                                                onclick="confirmDelete('{{ route('sales.destroy', $sale->id) }}', 'حذف المبيعة', 'هل أنت متأكد من حذف هذه المبيعة؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">لا توجد مبيعات</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($sales->hasPages())
            <div class="card-footer">
                {{ $sales->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Add any additional JavaScript here if needed
$(document).ready(function() {
    // Initialize any plugins or additional functionality
    console.log('Sales page loaded with notification system');
});
</script>
@endpush
