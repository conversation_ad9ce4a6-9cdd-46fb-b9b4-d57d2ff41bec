@extends('layouts.app')

@section('title', 'مبيعة جديدة')
@section('page-title', 'إضافة مبيعة جديدة')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="{{ route('sales.index') }}">المبيعات</a></li>
    <li class="breadcrumb-item active">مبيعة جديدة</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">بيانات المبيعة</h3>
                <div class="card-tools">
                    <a href="{{ route('sales.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </div>
            <form action="{{ route('sales.store') }}" method="POST" id="saleForm">
                @csrf
                <div class="card-body">
                    <div class="row">
                        <!-- Customer Selection -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="customer_id">العميل <span class="text-danger">*</span></label>
                                <select class="form-control @error('customer_id') is-invalid @enderror" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}" {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('customer_id')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- Sale Date -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sale_date">تاريخ البيع <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('sale_date') is-invalid @enderror"
                                       id="sale_date" name="sale_date" value="{{ old('sale_date', date('Y-m-d')) }}" required>
                                @error('sale_date')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Payment Method -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="payment_method">طريقة الدفع</label>
                                <select class="form-control @error('payment_method') is-invalid @enderror" id="payment_method" name="payment_method">
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>نقدي</option>
                                    <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                    <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                                    <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>شيك</option>
                                    <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>أخرى</option>
                                </select>
                                @error('payment_method')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">حالة البيع</label>
                                <select class="form-control @error('status') is-invalid @enderror" id="status" name="status">
                                    <option value="pending" {{ old('status', 'pending') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                    <option value="confirmed" {{ old('status') == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                                    <option value="shipped" {{ old('status') == 'shipped' ? 'selected' : '' }}>تم الشحن</option>
                                    <option value="delivered" {{ old('status') == 'delivered' ? 'selected' : '' }}>تم التسليم</option>
                                </select>
                                @error('status')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Sale Items -->
                    <div class="row">
                        <div class="col-12">
                            <h5>عناصر المبيعة</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="itemsTable">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>سعر الوحدة</th>
                                            <th>الخصم</th>
                                            <th>الإجمالي</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="itemsBody">
                                        <tr class="item-row">
                                            <td>
                                                <select class="form-control product-select" name="items[0][product_id]" required>
                                                    <option value="">اختر المنتج</option>
                                                    @foreach($products as $product)
                                                        <option value="{{ $product->id }}" data-price="{{ $product->selling_price }}">
                                                            {{ $product->name }} ({{ $product->sku }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control quantity-input" name="items[0][quantity]" min="1" value="1" required>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control price-input" name="items[0][unit_price]" step="0.01" min="0" required>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control discount-input" name="items[0][discount_amount]" step="0.01" min="0" value="0">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control total-input" readonly>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm remove-item">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <button type="button" class="btn btn-success" id="addItem">
                                <i class="fas fa-plus"></i> إضافة منتج
                            </button>
                        </div>
                    </div>

                    <!-- Totals -->
                    <div class="row mt-3">
                        <div class="col-md-6 ml-auto">
                            <table class="table">
                                <tr>
                                    <th>المبلغ الإجمالي:</th>
                                    <td><span id="totalAmount">0.00</span> ريال</td>
                                </tr>
                                <tr>
                                    <th>إجمالي الخصم:</th>
                                    <td><span id="totalDiscount">0.00</span> ريال</td>
                                </tr>
                                <tr>
                                    <th>الضريبة (15%):</th>
                                    <td><span id="totalTax">0.00</span> ريال</td>
                                </tr>
                                <tr class="table-active">
                                    <th>المبلغ الصافي:</th>
                                    <th><span id="netAmount">0.00</span> ريال</th>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Hidden inputs for totals -->
                    <input type="hidden" name="total_amount" id="hiddenTotalAmount">
                    <input type="hidden" name="tax_amount" id="hiddenTaxAmount">
                    <input type="hidden" name="discount_amount" id="hiddenDiscountAmount">

                    <!-- Notes -->
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="notes">ملاحظات</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror"
                                          id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <button type="submit" class="btn btn-primary" id="saveBtn">
                        <i class="fas fa-save"></i> حفظ المبيعة
                    </button>
                    <a href="{{ route('sales.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let itemIndex = 1;

$(document).ready(function() {
    // Add new item row
    $('#addItem').click(function() {
        addItemRow();
    });

    // Remove item row
    $(document).on('click', '.remove-item', function() {
        if ($('.item-row').length > 1) {
            $(this).closest('.item-row').remove();
            calculateTotals();
        }
    });

    // Product selection change
    $(document).on('change', '.product-select', function() {
        const price = $(this).find(':selected').data('price');
        $(this).closest('.item-row').find('.price-input').val(price);
        calculateRowTotal($(this).closest('.item-row'));
    });

    // Quantity or price change
    $(document).on('input', '.quantity-input, .price-input, .discount-input', function() {
        calculateRowTotal($(this).closest('.item-row'));
    });

    // Initial calculation
    calculateTotals();

    // Form submission with loading
    $('form').on('submit', function(e) {
        const saveBtn = $('#saveBtn');
        saveBtn.prop('disabled', true);
        saveBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');

        // Show loading notification
        showLoading('جاري حفظ المبيعة...');
    });
});

function addItemRow() {
    const newRow = `
        <tr class="item-row">
            <td>
                <select class="form-control product-select" name="items[${itemIndex}][product_id]" required>
                    <option value="">اختر المنتج</option>
                    @foreach($products as $product)
                        <option value="{{ $product->id }}" data-price="{{ $product->selling_price }}">
                            {{ $product->name }} ({{ $product->sku }})
                        </option>
                    @endforeach
                </select>
            </td>
            <td>
                <input type="number" class="form-control quantity-input" name="items[${itemIndex}][quantity]" min="1" value="1" required>
            </td>
            <td>
                <input type="number" class="form-control price-input" name="items[${itemIndex}][unit_price]" step="0.01" min="0" required>
            </td>
            <td>
                <input type="number" class="form-control discount-input" name="items[${itemIndex}][discount_amount]" step="0.01" min="0" value="0">
            </td>
            <td>
                <input type="number" class="form-control total-input" readonly>
            </td>
            <td>
                <button type="button" class="btn btn-danger btn-sm remove-item">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    $('#itemsBody').append(newRow);
    itemIndex++;
}

function calculateRowTotal(row) {
    const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
    const price = parseFloat(row.find('.price-input').val()) || 0;
    const discount = parseFloat(row.find('.discount-input').val()) || 0;

    const total = (quantity * price) - discount;
    row.find('.total-input').val(total.toFixed(2));

    calculateTotals();
}

function calculateTotals() {
    let totalAmount = 0;
    let totalDiscount = 0;

    $('.item-row').each(function() {
        const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
        const price = parseFloat($(this).find('.price-input').val()) || 0;
        const discount = parseFloat($(this).find('.discount-input').val()) || 0;

        totalAmount += quantity * price;
        totalDiscount += discount;
    });

    const taxRate = 0.15; // 15% tax
    const taxAmount = (totalAmount - totalDiscount) * taxRate;
    const netAmount = totalAmount - totalDiscount + taxAmount;

    $('#totalAmount').text(totalAmount.toFixed(2));
    $('#totalDiscount').text(totalDiscount.toFixed(2));
    $('#totalTax').text(taxAmount.toFixed(2));
    $('#netAmount').text(netAmount.toFixed(2));

    $('#hiddenTotalAmount').val(totalAmount.toFixed(2));
    $('#hiddenTaxAmount').val(taxAmount.toFixed(2));
    $('#hiddenDiscountAmount').val(totalDiscount.toFixed(2));
}
</script>
@endpush
