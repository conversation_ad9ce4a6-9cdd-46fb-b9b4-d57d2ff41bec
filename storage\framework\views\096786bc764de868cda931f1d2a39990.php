<p class="text-muted mb-3">
    بمجرد حذف حسابك، سيتم حذف جميع موارده وبياناته نهائياً. قبل حذف حسابك، يرجى تنزيل أي بيانات أو معلومات تريد الاحتفاظ بها.
</p>

<button type="button" class="btn btn-danger" data-toggle="modal" data-target="#deleteAccountModal">
    <i class="fas fa-trash"></i> حذف الحساب
</button>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title text-white">
                    <i class="fas fa-exclamation-triangle"></i>
                    تأكيد حذف الحساب
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>

            <form method="post" action="<?php echo e(route('profile.destroy')); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('delete'); ?>

                <div class="modal-body">
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> هل أنت متأكد من حذف حسابك؟</h6>
                        <p class="mb-0">
                            بمجرد حذف حسابك، سيتم حذف جميع موارده وبياناته نهائياً.
                            يرجى إدخال كلمة المرور لتأكيد رغبتك في حذف حسابك نهائياً.
                        </p>
                    </div>

                    <div class="form-group">
                        <label for="delete_password">كلمة المرور</label>
                        <input type="password"
                               class="form-control <?php $__errorArgs = ['password', 'userDeletion'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="delete_password"
                               name="password"
                               placeholder="أدخل كلمة المرور للتأكيد">
                        <?php $__errorArgs = ['password', 'userDeletion'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> حذف الحساب نهائياً
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php if($errors->userDeletion->isNotEmpty()): ?>
<script>
    $(document).ready(function() {
        $('#deleteAccountModal').modal('show');
    });
</script>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/profile/partials/delete-user-form.blade.php ENDPATH**/ ?>