<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Dashboard
            ['name' => 'view-dashboard', 'display_name' => 'عرض لوحة التحكم', 'group' => 'dashboard'],

            // Sales
            ['name' => 'view-sales', 'display_name' => 'عرض المبيعات', 'group' => 'sales'],
            ['name' => 'create-sales', 'display_name' => 'إنشاء مبيعات', 'group' => 'sales'],
            ['name' => 'edit-sales', 'display_name' => 'تعديل المبيعات', 'group' => 'sales'],
            ['name' => 'delete-sales', 'display_name' => 'حذف المبيعات', 'group' => 'sales'],

            // Customers
            ['name' => 'view-customers', 'display_name' => 'عرض العملاء', 'group' => 'customers'],
            ['name' => 'create-customers', 'display_name' => 'إنشاء عملاء', 'group' => 'customers'],
            ['name' => 'edit-customers', 'display_name' => 'تعديل العملاء', 'group' => 'customers'],
            ['name' => 'delete-customers', 'display_name' => 'حذف العملاء', 'group' => 'customers'],

            // Inventory
            ['name' => 'view-inventory', 'display_name' => 'عرض المخزون', 'group' => 'inventory'],
            ['name' => 'create-inventory', 'display_name' => 'إنشاء منتجات', 'group' => 'inventory'],
            ['name' => 'edit-inventory', 'display_name' => 'تعديل المنتجات', 'group' => 'inventory'],
            ['name' => 'delete-inventory', 'display_name' => 'حذف المنتجات', 'group' => 'inventory'],

            // Categories
            ['name' => 'view-categories', 'display_name' => 'عرض التصنيفات', 'group' => 'categories'],
            ['name' => 'create-categories', 'display_name' => 'إنشاء تصنيفات', 'group' => 'categories'],
            ['name' => 'edit-categories', 'display_name' => 'تعديل التصنيفات', 'group' => 'categories'],
            ['name' => 'delete-categories', 'display_name' => 'حذف التصنيفات', 'group' => 'categories'],

            // Suppliers
            ['name' => 'view-suppliers', 'display_name' => 'عرض الموردين', 'group' => 'suppliers'],
            ['name' => 'create-suppliers', 'display_name' => 'إنشاء موردين', 'group' => 'suppliers'],
            ['name' => 'edit-suppliers', 'display_name' => 'تعديل الموردين', 'group' => 'suppliers'],
            ['name' => 'delete-suppliers', 'display_name' => 'حذف الموردين', 'group' => 'suppliers'],

            // Accounting
            ['name' => 'view-accounting', 'display_name' => 'عرض المحاسبة', 'group' => 'accounting'],
            ['name' => 'view-accounts', 'display_name' => 'عرض الحسابات', 'group' => 'accounting'],
            ['name' => 'create-accounts', 'display_name' => 'إنشاء حسابات', 'group' => 'accounting'],
            ['name' => 'edit-accounts', 'display_name' => 'تعديل الحسابات', 'group' => 'accounting'],
            ['name' => 'delete-accounts', 'display_name' => 'حذف الحسابات', 'group' => 'accounting'],

            // Transactions
            ['name' => 'view-transactions', 'display_name' => 'عرض المعاملات', 'group' => 'transactions'],
            ['name' => 'create-transactions', 'display_name' => 'إنشاء معاملات', 'group' => 'transactions'],
            ['name' => 'edit-transactions', 'display_name' => 'تعديل المعاملات', 'group' => 'transactions'],
            ['name' => 'delete-transactions', 'display_name' => 'حذف المعاملات', 'group' => 'transactions'],
            ['name' => 'reconcile-transactions', 'display_name' => 'تسوية المعاملات', 'group' => 'transactions'],

            // Reports
            ['name' => 'view-reports', 'display_name' => 'عرض التقارير', 'group' => 'reports'],
            ['name' => 'export-reports', 'display_name' => 'تصدير التقارير', 'group' => 'reports'],

            // User Management
            ['name' => 'view-users', 'display_name' => 'عرض المستخدمين', 'group' => 'users'],
            ['name' => 'create-users', 'display_name' => 'إنشاء مستخدمين', 'group' => 'users'],
            ['name' => 'edit-users', 'display_name' => 'تعديل المستخدمين', 'group' => 'users'],
            ['name' => 'delete-users', 'display_name' => 'حذف المستخدمين', 'group' => 'users'],

            // Role Management
            ['name' => 'view-roles', 'display_name' => 'عرض الأدوار', 'group' => 'roles'],
            ['name' => 'create-roles', 'display_name' => 'إنشاء أدوار', 'group' => 'roles'],
            ['name' => 'edit-roles', 'display_name' => 'تعديل الأدوار', 'group' => 'roles'],
            ['name' => 'delete-roles', 'display_name' => 'حذف الأدوار', 'group' => 'roles'],

            // Settings
            ['name' => 'view-settings', 'display_name' => 'عرض الإعدادات', 'group' => 'settings'],
            ['name' => 'edit-settings', 'display_name' => 'تعديل الإعدادات', 'group' => 'settings'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }

        // Create roles
        $superAdmin = Role::firstOrCreate(
            ['name' => 'super-admin'],
            [
                'display_name' => 'مدير عام',
                'description' => 'صلاحيات كاملة لجميع أجزاء النظام'
            ]
        );

        $admin = Role::firstOrCreate(
            ['name' => 'admin'],
            [
                'display_name' => 'مدير',
                'description' => 'صلاحيات إدارية للنظام'
            ]
        );

        $manager = Role::firstOrCreate(
            ['name' => 'manager'],
            [
                'display_name' => 'مدير قسم',
                'description' => 'صلاحيات إدارة الأقسام'
            ]
        );

        $user = Role::firstOrCreate(
            ['name' => 'user'],
            [
                'display_name' => 'مستخدم',
                'description' => 'صلاحيات أساسية للمستخدم'
            ]
        );

        // Assign permissions to roles
        $superAdmin->givePermissionTo(Permission::all());

        $admin->givePermissionTo([
            'view-dashboard',
            'view-sales', 'create-sales', 'edit-sales', 'delete-sales',
            'view-customers', 'create-customers', 'edit-customers', 'delete-customers',
            'view-inventory', 'create-inventory', 'edit-inventory', 'delete-inventory',
            'view-categories', 'create-categories', 'edit-categories', 'delete-categories',
            'view-suppliers', 'create-suppliers', 'edit-suppliers', 'delete-suppliers',
            'view-accounting', 'view-accounts', 'create-accounts', 'edit-accounts',
            'view-transactions', 'create-transactions', 'edit-transactions', 'reconcile-transactions',
            'view-reports', 'export-reports',
            'view-users', 'create-users', 'edit-users',
        ]);

        $manager->givePermissionTo([
            'view-dashboard',
            'view-sales', 'create-sales', 'edit-sales',
            'view-customers', 'create-customers', 'edit-customers',
            'view-inventory', 'create-inventory', 'edit-inventory',
            'view-categories', 'create-categories', 'edit-categories',
            'view-suppliers', 'create-suppliers', 'edit-suppliers',
            'view-accounting', 'view-accounts', 'view-transactions',
            'view-reports',
        ]);

        $user->givePermissionTo([
            'view-dashboard',
            'view-sales', 'create-sales',
            'view-customers', 'create-customers',
            'view-inventory',
            'view-categories',
            'view-suppliers',
            'view-accounting', 'view-accounts', 'view-transactions',
        ]);

        $this->command->info('Permissions and roles created successfully!');
    }
}
