<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Supplier;
use Illuminate\Http\Request;

class SupplierController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $suppliers = Supplier::withCount('products')
            ->latest()
            ->paginate(15);

        return view('suppliers.index', compact('suppliers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('suppliers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'company_name' => 'nullable|string|max:255',
                'email' => 'nullable|email|unique:suppliers,email',
                'phone' => 'required|string|max:20',
                'mobile' => 'nullable|string|max:20',
                'address' => 'nullable|string|max:500',
                'city' => 'nullable|string|max:100',
                'country' => 'nullable|string|max:100',
                'postal_code' => 'nullable|string|max:20',
                'tax_number' => 'nullable|string|max:50',
                'commercial_register' => 'nullable|string|max:50',
                'contact_person' => 'nullable|string|max:255',
                'website' => 'nullable|url|max:255',
                'payment_terms' => 'nullable|integer|min:0|max:365',
                'credit_limit' => 'nullable|numeric|min:0',
                'status' => 'required|in:active,inactive',
                'supplier_type' => 'required|in:company,individual,government,international',
                'bank_name' => 'nullable|string|max:255',
                'bank_account' => 'nullable|string|max:50',
                'iban' => 'nullable|string|max:50',
                'notes' => 'nullable|string|max:1000'
            ]);

            // Create supplier
            $supplier = Supplier::create($request->all());

            // Success notification
            notify_crud('created', 'supplier');

            return redirect()->route('suppliers.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'supplier', false);
            
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Supplier $supplier)
    {
        $supplier->load(['products' => function($query) {
            $query->latest()->take(10);
        }]);

        $stats = [
            'products_count' => $supplier->products()->count(),
            'active_products' => $supplier->products()->where('status', 'active')->count(),
            'total_purchases' => 0, // Will be implemented when purchase orders are ready
            'last_purchase_date' => null
        ];

        return view('suppliers.show', compact('supplier', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Supplier $supplier)
    {
        return view('suppliers.edit', compact('supplier'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Supplier $supplier)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'company_name' => 'nullable|string|max:255',
                'email' => 'nullable|email|unique:suppliers,email,' . $supplier->id,
                'phone' => 'required|string|max:20',
                'mobile' => 'nullable|string|max:20',
                'address' => 'nullable|string|max:500',
                'city' => 'nullable|string|max:100',
                'country' => 'nullable|string|max:100',
                'postal_code' => 'nullable|string|max:20',
                'tax_number' => 'nullable|string|max:50',
                'commercial_register' => 'nullable|string|max:50',
                'contact_person' => 'nullable|string|max:255',
                'website' => 'nullable|url|max:255',
                'payment_terms' => 'nullable|integer|min:0|max:365',
                'credit_limit' => 'nullable|numeric|min:0',
                'status' => 'required|in:active,inactive',
                'supplier_type' => 'required|in:company,individual,government,international',
                'bank_name' => 'nullable|string|max:255',
                'bank_account' => 'nullable|string|max:50',
                'iban' => 'nullable|string|max:50',
                'notes' => 'nullable|string|max:1000'
            ]);

            // Update supplier
            $supplier->update($request->all());

            // Success notification
            notify_crud('updated', 'supplier');

            return redirect()->route('suppliers.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'supplier', false);
            
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Supplier $supplier)
    {
        try {
            // Check if supplier has products
            if ($supplier->products()->count() > 0) {
                notify_error('لا يمكن حذف المورد لأنه يحتوي على منتجات', 'خطأ في الحذف');
                return back();
            }

            $supplier->delete();

            // Success notification
            notify_crud('deleted', 'supplier');

            return redirect()->route('suppliers.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'supplier', false);
            
            return back();
        }
    }

    /**
     * Restore a soft deleted supplier.
     */
    public function restore($id)
    {
        try {
            $supplier = Supplier::withTrashed()->findOrFail($id);
            $supplier->restore();

            notify_crud('restored', 'supplier');

            return redirect()->route('suppliers.index');

        } catch (\Exception $e) {
            notify_crud('restored', 'supplier', false);
            return back();
        }
    }

    /**
     * Get supplier statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_suppliers' => Supplier::count(),
            'active_suppliers' => Supplier::active()->count(),
            'inactive_suppliers' => Supplier::where('status', 'inactive')->count(),
            'company_suppliers' => Supplier::where('supplier_type', 'company')->count(),
            'individual_suppliers' => Supplier::where('supplier_type', 'individual')->count(),
            'international_suppliers' => Supplier::where('supplier_type', 'international')->count()
        ];

        return response()->json($stats);
    }

    /**
     * Get suppliers for select options.
     */
    public function getForSelect()
    {
        $suppliers = Supplier::active()
            ->select('id', 'name', 'company_name')
            ->orderBy('name')
            ->get()
            ->map(function($supplier) {
                return [
                    'id' => $supplier->id,
                    'name' => $supplier->company_name ?: $supplier->name
                ];
            });

        return response()->json($suppliers);
    }
}
