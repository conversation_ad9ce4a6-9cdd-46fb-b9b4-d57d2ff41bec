<p class="text-muted mb-3">
    تأكد من أن حسابك يستخدم كلمة مرور طويلة وعشوائية للبقاء آمناً.
</p>

<form method="post" action="{{ route('password.update') }}">
    @csrf
    @method('put')

    <div class="form-group">
        <label for="current_password">كلمة المرور الحالية</label>
        <input type="password"
               class="form-control @error('current_password', 'updatePassword') is-invalid @enderror"
               id="current_password"
               name="current_password"
               autocomplete="current-password">
        @error('current_password', 'updatePassword')
            <span class="invalid-feedback">{{ $message }}</span>
        @enderror
    </div>

    <div class="form-group">
        <label for="password">كلمة المرور الجديدة</label>
        <input type="password"
               class="form-control @error('password', 'updatePassword') is-invalid @enderror"
               id="password"
               name="password"
               autocomplete="new-password">
        @error('password', 'updatePassword')
            <span class="invalid-feedback">{{ $message }}</span>
        @enderror
    </div>

    <div class="form-group">
        <label for="password_confirmation">تأكيد كلمة المرور</label>
        <input type="password"
               class="form-control @error('password_confirmation', 'updatePassword') is-invalid @enderror"
               id="password_confirmation"
               name="password_confirmation"
               autocomplete="new-password">
        @error('password_confirmation', 'updatePassword')
            <span class="invalid-feedback">{{ $message }}</span>
        @enderror
    </div>

    <div class="form-group">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> حفظ
        </button>

        @if (session('status') === 'password-updated')
            <span class="text-success ml-3">
                <i class="fas fa-check"></i> تم تحديث كلمة المرور بنجاح.
            </span>
        @endif
    </div>
</form>
