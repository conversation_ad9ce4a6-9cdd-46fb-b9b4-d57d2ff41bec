<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Sales\Customer;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Product;
use App\Models\Inventory\Category;
use App\Models\Sales\Sale;
use App\Models\Accounting\Account;
use App\Models\Accounting\Transaction;
use App\Models\User;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating demo data...');

        // Create demo customers
        $this->createCustomers();

        // Create demo suppliers
        $this->createSuppliers();

        // Create demo categories
        $this->createCategories();

        // Create demo products
        $this->createProducts();

        // Create demo accounts
        $this->createAccounts();

        // Create demo transactions
        $this->createTransactions();

        // Create demo sales
        $this->createSales();

        $this->command->info('Demo data created successfully!');
    }

    private function createCustomers()
    {
        $customers = [
            [
                'name' => 'أحمد محمد علي',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'address' => 'شارع الملك فهد، الرياض',
                'city' => 'الرياض',
                'country' => 'المملكة العربية السعودية',
                'customer_type' => 'individual',
                'status' => 'active',
                'credit_limit' => 10000,
                'payment_terms' => 30
            ],
            [
                'name' => 'شركة التقنية المتقدمة',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'address' => 'طريق الملك عبدالعزيز، جدة',
                'city' => 'جدة',
                'country' => 'المملكة العربية السعودية',
                'customer_type' => 'company',
                'status' => 'active',
                'credit_limit' => 50000,
                'payment_terms' => 45,
                'tax_number' => '*********'
            ],
            [
                'name' => 'فاطمة عبدالله',
                'email' => '<EMAIL>',
                'phone' => '+966509876543',
                'address' => 'شارع الأمير سلطان، الدمام',
                'city' => 'الدمام',
                'country' => 'المملكة العربية السعودية',
                'customer_type' => 'individual',
                'status' => 'active',
                'credit_limit' => 15000,
                'payment_terms' => 30
            ]
        ];

        foreach ($customers as $customer) {
            Customer::create($customer);
        }

        $this->command->info('✓ Customers created');
    }

    private function createSuppliers()
    {
        $suppliers = [
            [
                'name' => 'محمد أحمد التجاري',
                'company_name' => 'مؤسسة محمد أحمد التجارية',
                'email' => '<EMAIL>',
                'phone' => '+966501111111',
                'address' => 'حي العليا، الرياض',
                'city' => 'الرياض',
                'country' => 'المملكة العربية السعودية',
                'status' => 'active',
                'payment_terms' => 30,
                'contact_person' => 'محمد أحمد',
                'contact_phone' => '+966501111111',
                'contact_email' => '<EMAIL>'
            ],
            [
                'name' => 'شركة الإلكترونيات الحديثة',
                'company_name' => 'شركة الإلكترونيات الحديثة المحدودة',
                'email' => '<EMAIL>',
                'phone' => '+966112222222',
                'address' => 'شارع التحلية، جدة',
                'city' => 'جدة',
                'country' => 'المملكة العربية السعودية',
                'status' => 'active',
                'payment_terms' => 45,
                'tax_number' => '*********',
                'contact_person' => 'أحمد محمد',
                'contact_phone' => '+966112222222',
                'contact_email' => '<EMAIL>'
            ]
        ];

        foreach ($suppliers as $supplier) {
            Supplier::create($supplier);
        }

        $this->command->info('✓ Suppliers created');
    }

    private function createCategories()
    {
        $categories = [
            [
                'name' => 'إلكترونيات',
                'slug' => 'electronics',
                'description' => 'جميع المنتجات الإلكترونية',
                'status' => 'active'
            ],
            [
                'name' => 'أجهزة كمبيوتر',
                'slug' => 'computers',
                'description' => 'أجهزة الكمبيوتر واللابتوب',
                'parent_id' => 1,
                'status' => 'active'
            ],
            [
                'name' => 'هواتف ذكية',
                'slug' => 'smartphones',
                'description' => 'الهواتف الذكية والأجهزة اللوحية',
                'parent_id' => 1,
                'status' => 'active'
            ],
            [
                'name' => 'ملابس',
                'slug' => 'clothing',
                'description' => 'الملابس والأزياء',
                'status' => 'active'
            ],
            [
                'name' => 'أدوات منزلية',
                'slug' => 'home-appliances',
                'description' => 'الأدوات والأجهزة المنزلية',
                'status' => 'active'
            ]
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }

        $this->command->info('✓ Categories created');
    }

    private function createProducts()
    {
        $products = [
            [
                'name' => 'لابتوب Dell Inspiron 15',
                'description' => 'لابتوب Dell Inspiron 15 بمعالج Intel Core i5',
                'sku' => 'DELL-INS-15-001',
                'barcode' => '**********123',
                'category_id' => 2,
                'supplier_id' => 2,
                'price' => 2500.00,
                'cost_price' => 2000.00,
                'quantity_in_stock' => 25,
                'minimum_stock_level' => 5,
                'status' => 'active',
                'weight' => 2.5,
                'dimensions' => '35x25x2 سم'
            ],
            [
                'name' => 'iPhone 14 Pro',
                'description' => 'هاتف iPhone 14 Pro بذاكرة 128GB',
                'sku' => 'APPLE-IP14-PRO-128',
                'barcode' => '2345678901234',
                'category_id' => 3,
                'supplier_id' => 2,
                'price' => 4500.00,
                'cost_price' => 3800.00,
                'quantity_in_stock' => 15,
                'minimum_stock_level' => 3,
                'status' => 'active',
                'weight' => 0.2,
                'dimensions' => '15x7x0.8 سم'
            ],
            [
                'name' => 'سامسونج Galaxy S23',
                'description' => 'هاتف Samsung Galaxy S23 بذاكرة 256GB',
                'sku' => 'SAMSUNG-S23-256',
                'barcode' => '3456789012345',
                'category_id' => 3,
                'supplier_id' => 2,
                'price' => 3800.00,
                'cost_price' => 3200.00,
                'quantity_in_stock' => 8,
                'minimum_stock_level' => 5,
                'status' => 'active',
                'weight' => 0.18,
                'dimensions' => '14.5x7x0.7 سم'
            ],
            [
                'name' => 'ماوس لاسلكي Logitech',
                'description' => 'ماوس لاسلكي من Logitech بتقنية البلوتوث',
                'sku' => 'LOGI-MOUSE-BT-001',
                'barcode' => '4567890123456',
                'category_id' => 2,
                'supplier_id' => 1,
                'price' => 150.00,
                'cost_price' => 100.00,
                'quantity_in_stock' => 50,
                'minimum_stock_level' => 10,
                'status' => 'active',
                'weight' => 0.1,
                'dimensions' => '10x6x3 سم'
            ],
            [
                'name' => 'كيبورد ميكانيكي',
                'description' => 'كيبورد ميكانيكي للألعاب مع إضاءة RGB',
                'sku' => 'MECH-KB-RGB-001',
                'barcode' => '5678901234567',
                'category_id' => 2,
                'supplier_id' => 1,
                'price' => 350.00,
                'cost_price' => 250.00,
                'quantity_in_stock' => 2,
                'minimum_stock_level' => 5,
                'status' => 'active',
                'weight' => 1.2,
                'dimensions' => '45x15x3 سم'
            ]
        ];

        foreach ($products as $product) {
            Product::create($product);
        }

        $this->command->info('✓ Products created');
    }

    private function createAccounts()
    {
        $accounts = [
            [
                'name' => 'الصندوق',
                'code' => 'AST-CAS-001',
                'type' => 'asset',
                'balance' => 50000.00,
                'currency' => 'SAR',
                'status' => 'active',
                'is_default' => true,
                'description' => 'حساب النقدية في الصندوق'
            ],
            [
                'name' => 'البنك الأهلي - الحساب الجاري',
                'code' => 'AST-BNK-001',
                'type' => 'asset',
                'balance' => 150000.00,
                'currency' => 'SAR',
                'status' => 'active',
                'bank_name' => 'البنك الأهلي السعودي',
                'account_number' => '**********',
                'description' => 'الحساب الجاري في البنك الأهلي'
            ],
            [
                'name' => 'مبيعات المنتجات',
                'code' => 'REV-SAL-001',
                'type' => 'revenue',
                'balance' => 0.00,
                'currency' => 'SAR',
                'status' => 'active',
                'is_default' => true,
                'description' => 'إيرادات مبيعات المنتجات'
            ],
            [
                'name' => 'مصروفات التشغيل',
                'code' => 'EXP-OPR-001',
                'type' => 'expense',
                'balance' => 0.00,
                'currency' => 'SAR',
                'status' => 'active',
                'description' => 'مصروفات التشغيل العامة'
            ]
        ];

        foreach ($accounts as $account) {
            Account::create($account);
        }

        $this->command->info('✓ Accounts created');
    }

    private function createTransactions()
    {
        $transactions = [
            [
                'account_id' => 1,
                'type' => 'income',
                'amount' => 5000.00,
                'description' => 'إيداع نقدي ابتدائي',
                'transaction_date' => now()->subDays(10),
                'payment_method' => 'cash',
                'status' => 'completed',
                'reference_number' => 'INC-' . date('Ymd') . '-001',
                'user_id' => 1
            ],
            [
                'account_id' => 2,
                'type' => 'income',
                'amount' => 25000.00,
                'description' => 'تحويل بنكي من العميل',
                'transaction_date' => now()->subDays(8),
                'payment_method' => 'bank_transfer',
                'status' => 'completed',
                'reference_number' => 'INC-' . date('Ymd') . '-002',
                'user_id' => 1
            ],
            [
                'account_id' => 4,
                'type' => 'expense',
                'amount' => 1500.00,
                'description' => 'مصروفات إيجار المكتب',
                'transaction_date' => now()->subDays(5),
                'payment_method' => 'bank_transfer',
                'status' => 'completed',
                'reference_number' => 'EXP-' . date('Ymd') . '-001',
                'user_id' => 1
            ]
        ];

        foreach ($transactions as $transaction) {
            Transaction::create($transaction);
        }

        $this->command->info('✓ Transactions created');
    }

    private function createSales()
    {
        $sales = [
            [
                'customer_id' => 1,
                'sale_date' => now()->subDays(3),
                'subtotal' => 2650.00,
                'tax_amount' => 397.50,
                'discount_amount' => 0.00,
                'total_amount' => 3047.50,
                'status' => 'completed',
                'payment_status' => 'paid',
                'payment_method' => 'cash',
                'notes' => 'بيع لابتوب وماوس للعميل أحمد',
                'user_id' => 1
            ],
            [
                'customer_id' => 2,
                'sale_date' => now()->subDays(1),
                'subtotal' => 4500.00,
                'tax_amount' => 675.00,
                'discount_amount' => 200.00,
                'total_amount' => 4975.00,
                'status' => 'completed',
                'payment_status' => 'paid',
                'payment_method' => 'bank_transfer',
                'notes' => 'بيع iPhone 14 Pro لشركة التقنية المتقدمة',
                'user_id' => 1
            ]
        ];

        foreach ($sales as $sale) {
            Sale::create($sale);
        }

        $this->command->info('✓ Sales created');
    }
}
