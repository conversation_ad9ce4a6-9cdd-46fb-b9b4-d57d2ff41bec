@extends('layouts.app')

@section('title', 'الأدوار والصلاحيات')
@section('page-title', 'إدارة الأدوار والصلاحيات')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">الأدوار والصلاحيات</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة الأدوار</h3>
                <div class="card-tools">
                    <a href="{{ route('roles.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> دور جديد
                    </a>
                    <a href="{{ route('users.index') }}" class="btn btn-info">
                        <i class="fas fa-users"></i> إدارة المستخدمين
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>اسم الدور</th>
                                <th>الاسم المعروض</th>
                                <th>الوصف</th>
                                <th>عدد المستخدمين</th>
                                <th>عدد الصلاحيات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($roles as $role)
                            <tr>
                                <td>
                                    <code>{{ $role->name }}</code>
                                    @if(in_array($role->name, ['super-admin', 'admin']))
                                        <span class="badge badge-warning badge-sm">نظام</span>
                                    @endif
                                </td>
                                <td>
                                    <strong>{{ $role->display_name ?? $role->name }}</strong>
                                </td>
                                <td>{{ Str::limit($role->description, 50) ?: '-' }}</td>
                                <td>
                                    <span class="badge badge-primary">{{ $role->users_count }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ $role->permissions_count }}</span>
                                </td>
                                <td>{{ $role->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('roles.show', $role->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('roles.edit', $role->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if(!in_array($role->name, ['super-admin', 'admin', 'user']) && $role->users_count == 0)
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('{{ route('roles.destroy', $role->id) }}', 'حذف الدور', 'هل أنت متأكد من حذف هذا الدور؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center">لا توجد أدوار</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($roles->hasPages())
            <div class="card-footer">
                {{ $roles->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $roles->total() }}</h3>
                <p>إجمالي الأدوار</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-tag"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $roles->where('users_count', '>', 0)->count() }}</h3>
                <p>أدوار مُستخدمة</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $roles->sum('users_count') }}</h3>
                <p>إجمالي التعيينات</p>
            </div>
            <div class="icon">
                <i class="fas fa-link"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $roles->where('users_count', 0)->count() }}</h3>
                <p>أدوار غير مُستخدمة</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-slash"></i>
            </div>
        </div>
    </div>
</div>

<!-- System Roles Info -->
<div class="row">
    <div class="col-12">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">معلومات الأدوار</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-danger"><i class="fas fa-crown"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Super Admin</span>
                                <span class="info-box-number">جميع الصلاحيات</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-warning"><i class="fas fa-user-shield"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Admin</span>
                                <span class="info-box-number">صلاحيات إدارية</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-user"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">User</span>
                                <span class="info-box-number">صلاحيات أساسية</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="alert alert-info">
                    <h5><i class="icon fas fa-info"></i> ملاحظة!</h5>
                    الأدوار المميزة بـ "نظام" هي أدوار أساسية ولا يمكن حذفها. يمكن تعديل صلاحياتها ولكن لا يُنصح بذلك.
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    console.log('Roles page loaded with notification system');
});
</script>
@endpush
