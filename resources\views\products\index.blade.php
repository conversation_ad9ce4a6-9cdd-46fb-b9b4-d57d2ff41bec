@extends('layouts.app')

@section('title', 'المنتجات')
@section('page-title', 'إدارة المنتجات')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">المنتجات</li>
@endsection

@section('content')
<!-- Filters -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">فلترة المنتجات</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('products.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="search">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="اسم المنتج، SKU، أو الباركود">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="category_id">التصنيف</label>
                                <select class="form-control" id="category_id" name="category_id">
                                    <option value="">جميع التصنيفات</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="supplier_id">المورد</label>
                                <select class="form-control" id="supplier_id" name="supplier_id">
                                    <option value="">جميع الموردين</option>
                                    @foreach($suppliers as $supplier)
                                        <option value="{{ $supplier->id }}" {{ request('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                            {{ $supplier->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="status">الحالة</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="stock_status">حالة المخزون</label>
                                <select class="form-control" id="stock_status" name="stock_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="in_stock" {{ request('stock_status') == 'in_stock' ? 'selected' : '' }}>متوفر</option>
                                    <option value="low_stock" {{ request('stock_status') == 'low_stock' ? 'selected' : '' }}>مخزون منخفض</option>
                                    <option value="out_of_stock" {{ request('stock_status') == 'out_of_stock' ? 'selected' : '' }}>نفد المخزون</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Products List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة المنتجات</h3>
                <div class="card-tools">
                    <a href="{{ route('products.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> منتج جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>اسم المنتج</th>
                                <th>SKU</th>
                                <th>التصنيف</th>
                                <th>المورد</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($products as $product)
                            <tr>
                                <td>
                                    @if($product->image)
                                        <img src="{{ asset('storage/' . $product->image) }}" 
                                             alt="{{ $product->name }}" 
                                             class="img-thumbnail" 
                                             style="width: 50px; height: 50px; object-fit: cover;">
                                    @else
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    <strong>{{ $product->name }}</strong>
                                    @if($product->description)
                                        <br><small class="text-muted">{{ Str::limit($product->description, 50) }}</small>
                                    @endif
                                </td>
                                <td>
                                    <code>{{ $product->sku }}</code>
                                    @if($product->barcode)
                                        <br><small class="text-muted">{{ $product->barcode }}</small>
                                    @endif
                                </td>
                                <td>{{ $product->category->name ?? '-' }}</td>
                                <td>{{ $product->supplier->name ?? '-' }}</td>
                                <td>
                                    <strong>{{ number_format($product->price, 2) }}</strong>
                                    @if($product->cost_price)
                                        <br><small class="text-muted">التكلفة: {{ number_format($product->cost_price, 2) }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge-{{ $product->quantity_in_stock <= $product->minimum_stock_level ? 'danger' : 'success' }}">
                                        {{ $product->quantity_in_stock }}
                                    </span>
                                    <br><small class="text-muted">الحد الأدنى: {{ $product->minimum_stock_level }}</small>
                                    @if($product->quantity_in_stock <= $product->minimum_stock_level)
                                        <br><small class="text-danger"><i class="fas fa-exclamation-triangle"></i> مخزون منخفض</small>
                                    @endif
                                </td>
                                <td>
                                    @if($product->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @else
                                        <span class="badge badge-danger">غير نشط</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('products.show', $product->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-success btn-sm" title="تحديث المخزون" 
                                                data-toggle="modal" data-target="#stockModal{{ $product->id }}">
                                            <i class="fas fa-boxes"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('{{ route('products.destroy', $product->id) }}', 'حذف المنتج', 'هل أنت متأكد من حذف هذا المنتج؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- Stock Update Modal -->
                            <div class="modal fade" id="stockModal{{ $product->id }}" tabindex="-1" role="dialog">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تحديث مخزون: {{ $product->name }}</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <form action="{{ route('products.update-stock', $product->id) }}" method="POST">
                                            @csrf
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label>المخزون الحالي</label>
                                                    <input type="text" class="form-control" value="{{ $product->quantity_in_stock }}" readonly>
                                                </div>
                                                <div class="form-group">
                                                    <label for="type">نوع العملية</label>
                                                    <select class="form-control" name="type" required>
                                                        <option value="add">إضافة</option>
                                                        <option value="subtract">خصم</option>
                                                        <option value="set">تعيين</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="quantity">الكمية</label>
                                                    <input type="number" class="form-control" name="quantity" min="0" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="reason">السبب</label>
                                                    <input type="text" class="form-control" name="reason" placeholder="سبب التحديث (اختياري)">
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                                <button type="submit" class="btn btn-primary">تحديث</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center">لا توجد منتجات</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($products->hasPages())
            <div class="card-footer">
                {{ $products->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $products->total() }}</h3>
                <p>إجمالي المنتجات</p>
            </div>
            <div class="icon">
                <i class="fas fa-boxes"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $products->where('status', 'active')->count() }}</h3>
                <p>المنتجات النشطة</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $products->filter(function($product) { return $product->quantity_in_stock <= $product->minimum_stock_level; })->count() }}</h3>
                <p>مخزون منخفض</p>
            </div>
            <div class="icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $products->where('quantity_in_stock', 0)->count() }}</h3>
                <p>نفد المخزون</p>
            </div>
            <div class="icon">
                <i class="fas fa-times-circle"></i>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    console.log('Products page loaded with notification system');
});
</script>
@endpush
