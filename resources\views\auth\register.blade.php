@extends('layouts.auth')

@section('title', 'إنشاء حساب جديد')

@section('content')
<div class="card">
    <div class="card-header">
        <div class="login-logo">
            <i class="fas fa-user-plus"></i>
            <strong>OmniFlow</strong> ERP
        </div>
        <p class="text-muted">إنشاء حساب جديد</p>
    </div>

    <div class="card-body">
        <form method="POST" action="{{ route('register') }}">
            @csrf

            <!-- Name -->
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                    </div>
                    <input type="text"
                           class="form-control @error('name') is-invalid @enderror"
                           name="name"
                           value="{{ old('name') }}"
                           placeholder="الاسم الكامل"
                           required
                           autofocus
                           autocomplete="name">
                </div>
                @error('name')
                    <span class="invalid-feedback d-block">{{ $message }}</span>
                @enderror
            </div>

            <!-- Email Address -->
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                    </div>
                    <input type="email"
                           class="form-control @error('email') is-invalid @enderror"
                           name="email"
                           value="{{ old('email') }}"
                           placeholder="البريد الإلكتروني"
                           required
                           autocomplete="username">
                </div>
                @error('email')
                    <span class="invalid-feedback d-block">{{ $message }}</span>
                @enderror
            </div>

            <!-- Password -->
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                    <input type="password"
                           class="form-control @error('password') is-invalid @enderror"
                           name="password"
                           placeholder="كلمة المرور"
                           required
                           autocomplete="new-password">
                </div>
                @error('password')
                    <span class="invalid-feedback d-block">{{ $message }}</span>
                @enderror
            </div>

            <!-- Confirm Password -->
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                    <input type="password"
                           class="form-control @error('password_confirmation') is-invalid @enderror"
                           name="password_confirmation"
                           placeholder="تأكيد كلمة المرور"
                           required
                           autocomplete="new-password">
                </div>
                @error('password_confirmation')
                    <span class="invalid-feedback d-block">{{ $message }}</span>
                @enderror
            </div>

            <!-- Submit Button -->
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block">
                    <i class="fas fa-user-plus mr-2"></i>
                    إنشاء الحساب
                </button>
            </div>

            <!-- Links -->
            <div class="text-center">
                <a href="{{ route('login') }}" class="text-link">
                    لديك حساب بالفعل؟ تسجيل الدخول
                </a>
            </div>
        </form>
    </div>
</div>
@endsection
