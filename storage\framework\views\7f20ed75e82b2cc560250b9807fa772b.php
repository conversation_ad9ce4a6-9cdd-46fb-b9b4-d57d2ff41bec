<p class="text-muted mb-3">
    تحديث معلومات الملف الشخصي والبريد الإلكتروني.
</p>

<form id="send-verification" method="post" action="<?php echo e(route('verification.send')); ?>">
    <?php echo csrf_field(); ?>
</form>

<form method="post" action="<?php echo e(route('profile.update')); ?>">
    <?php echo csrf_field(); ?>
    <?php echo method_field('patch'); ?>

    <div class="form-group">
        <label for="name">الاسم</label>
        <input type="text"
               class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
               id="name"
               name="name"
               value="<?php echo e(old('name', $user->name)); ?>"
               required
               autofocus
               autocomplete="name">
        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <span class="invalid-feedback"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="form-group">
        <label for="email">البريد الإلكتروني</label>
        <input type="email"
               class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
               id="email"
               name="email"
               value="<?php echo e(old('email', $user->email)); ?>"
               required
               autocomplete="username">
        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <span class="invalid-feedback"><?php echo e($message); ?></span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

        <?php if($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail()): ?>
            <div class="alert alert-warning mt-2">
                <p class="mb-2">
                    <i class="fas fa-exclamation-triangle"></i>
                    بريدك الإلكتروني غير مؤكد.
                </p>
                <button form="send-verification" class="btn btn-sm btn-outline-warning">
                    إرسال رابط التأكيد مرة أخرى
                </button>

                <?php if(session('status') === 'verification-link-sent'): ?>
                    <p class="mt-2 text-success">
                        <i class="fas fa-check"></i>
                        تم إرسال رابط تأكيد جديد إلى بريدك الإلكتروني.
                    </p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> حفظ
        </button>

        <?php if(session('status') === 'profile-updated'): ?>
            <span class="text-success ml-3">
                <i class="fas fa-check"></i> تم الحفظ بنجاح.
            </span>
        <?php endif; ?>
    </div>
</form>
<?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/profile/partials/update-profile-information-form.blade.php ENDPATH**/ ?>