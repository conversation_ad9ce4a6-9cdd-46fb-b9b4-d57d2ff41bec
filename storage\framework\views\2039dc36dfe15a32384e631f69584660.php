<?php $__env->startSection('title', 'لوحة التحكم'); ?>
<?php $__env->startSection('page-title', 'لوحة التحكم'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item active">لوحة التحكم</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Small boxes (Stat box) -->
<div class="row">
    <div class="col-lg-3 col-6">
        <!-- small box -->
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($stats['total_sales']); ?></h3>
                <p>إجمالي المبيعات</p>
            </div>
            <div class="icon">
                <i class="ion ion-bag"></i>
            </div>
            <a href="<?php echo e(route('sales.index')); ?>" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <!-- ./col -->
    <div class="col-lg-3 col-6">
        <!-- small box -->
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($stats['total_products']); ?></h3>
                <p>إجمالي المنتجات</p>
            </div>
            <div class="icon">
                <i class="ion ion-stats-bars"></i>
            </div>
            <a href="<?php echo e(route('inventory.index')); ?>" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <!-- ./col -->
    <div class="col-lg-3 col-6">
        <!-- small box -->
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($stats['total_customers']); ?></h3>
                <p>إجمالي العملاء</p>
            </div>
            <div class="icon">
                <i class="ion ion-person-add"></i>
            </div>
            <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <!-- ./col -->
    <div class="col-lg-3 col-6">
        <!-- small box -->
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($stats['low_stock_products']); ?></h3>
                <p>منتجات منخفضة المخزون</p>
            </div>
            <div class="icon">
                <i class="ion ion-pie-graph"></i>
            </div>
            <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
        </div>
    </div>
    <!-- ./col -->
</div>
<!-- /.row -->

<!-- Main row -->
<div class="row">
    <!-- Left col -->
    <section class="col-lg-7 connectedSortable">
        <!-- Recent Sales -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shopping-cart mr-1"></i>
                    المبيعات الأخيرة
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الرقم المرجعي</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $recent_sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($sale->reference_number); ?></td>
                                <td><?php echo e($sale->customer->name); ?></td>
                                <td><?php echo e(number_format($sale->total_amount, 2)); ?> ريال</td>
                                <td><?php echo e($sale->created_at->format('d/m/Y')); ?></td>
                                <td>
                                    <?php if($sale->status == 'pending'): ?>
                                        <span class="badge badge-warning">في الانتظار</span>
                                    <?php elseif($sale->status == 'confirmed'): ?>
                                        <span class="badge badge-success">مؤكد</span>
                                    <?php else: ?>
                                        <span class="badge badge-info"><?php echo e($sale->status); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="text-center">لا توجد مبيعات</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>
    <!-- /.Left col -->

    <!-- right col (We are only adding the ID to make the widgets sortable)-->
    <section class="col-lg-5 connectedSortable">
        <!-- Low Stock Products -->
        <div class="card bg-gradient-warning">
            <div class="card-header border-0">
                <h3 class="card-title">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    تنبيهات المخزون
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php $__empty_1 = true; $__currentLoopData = $low_stock_products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="d-flex justify-content-between align-items-center border-bottom mb-3">
                    <div>
                        <h6 class="mb-0"><?php echo e($product->name); ?></h6>
                        <small>الكمية المتبقية: <?php echo e($product->quantity_in_stock); ?></small>
                    </div>
                    <span class="badge badge-danger">منخفض</span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <p class="text-center">لا توجد منتجات منخفضة المخزون</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">إجراءات سريعة</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <button type="button" class="btn btn-primary btn-block" onclick="quickAction('sales')">
                            <i class="fas fa-plus"></i> مبيعة جديدة
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-success btn-block" onclick="quickAction('inventory')">
                            <i class="fas fa-plus"></i> منتج جديد
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <button type="button" class="btn btn-info btn-block" onclick="quickAction('customer')">
                            <i class="fas fa-users"></i> عميل جديد
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-warning btn-block" onclick="quickAction('report')">
                            <i class="fas fa-file-alt"></i> تقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- right col -->
</div>
<!-- /.row (main row) -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function quickAction(action) {
    switch(action) {
        case 'sales':
            showNotification('info', 'انتقال إلى صفحة إنشاء مبيعة جديدة...', 'إنشاء مبيعة');
            setTimeout(() => {
                window.location.href = '<?php echo e(route("sales.create")); ?>';
            }, 1000);
            break;

        case 'inventory':
            showNotification('info', 'انتقال إلى صفحة إضافة منتج جديد...', 'إضافة منتج');
            setTimeout(() => {
                window.location.href = '<?php echo e(route("inventory.create")); ?>';
            }, 1000);
            break;

        case 'customer':
            showNotification('warning', 'صفحة إضافة العملاء قيد التطوير', 'قريباً');
            break;

        case 'report':
            showNotification('info', 'صفحة التقارير قيد التطوير', 'قريباً');
            break;
    }
}

// Demo notifications on page load
$(document).ready(function() {
    // Welcome notification
    setTimeout(() => {
        showNotification('success', 'مرحباً بك في نظام OmniFlow ERP', 'أهلاً وسهلاً');
    }, 500);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/dashboard/index.blade.php ENDPATH**/ ?>