<?php $__env->startSection('title', 'المنتجات'); ?>
<?php $__env->startSection('page-title', 'إدارة المنتجات'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">المنتجات</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Filters -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">فلترة المنتجات</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('products.index')); ?>">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="search">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo e(request('search')); ?>" placeholder="اسم المنتج، SKU، أو الباركود">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="category_id">التصنيف</label>
                                <select class="form-control" id="category_id" name="category_id">
                                    <option value="">جميع التصنيفات</option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>" <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                            <?php echo e($category->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="supplier_id">المورد</label>
                                <select class="form-control" id="supplier_id" name="supplier_id">
                                    <option value="">جميع الموردين</option>
                                    <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($supplier->id); ?>" <?php echo e(request('supplier_id') == $supplier->id ? 'selected' : ''); ?>>
                                            <?php echo e($supplier->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="status">الحالة</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>نشط</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="stock_status">حالة المخزون</label>
                                <select class="form-control" id="stock_status" name="stock_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="in_stock" <?php echo e(request('stock_status') == 'in_stock' ? 'selected' : ''); ?>>متوفر</option>
                                    <option value="low_stock" <?php echo e(request('stock_status') == 'low_stock' ? 'selected' : ''); ?>>مخزون منخفض</option>
                                    <option value="out_of_stock" <?php echo e(request('stock_status') == 'out_of_stock' ? 'selected' : ''); ?>>نفد المخزون</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Products List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة المنتجات</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> منتج جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>اسم المنتج</th>
                                <th>SKU</th>
                                <th>التصنيف</th>
                                <th>المورد</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <?php if($product->image): ?>
                                        <img src="<?php echo e(asset('storage/' . $product->image)); ?>" 
                                             alt="<?php echo e($product->name); ?>" 
                                             class="img-thumbnail" 
                                             style="width: 50px; height: 50px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo e($product->name); ?></strong>
                                    <?php if($product->description): ?>
                                        <br><small class="text-muted"><?php echo e(Str::limit($product->description, 50)); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <code><?php echo e($product->sku); ?></code>
                                    <?php if($product->barcode): ?>
                                        <br><small class="text-muted"><?php echo e($product->barcode); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($product->category->name ?? '-'); ?></td>
                                <td><?php echo e($product->supplier->name ?? '-'); ?></td>
                                <td>
                                    <strong><?php echo e(number_format($product->price, 2)); ?></strong>
                                    <?php if($product->cost_price): ?>
                                        <br><small class="text-muted">التكلفة: <?php echo e(number_format($product->cost_price, 2)); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo e($product->quantity_in_stock <= $product->minimum_stock_level ? 'danger' : 'success'); ?>">
                                        <?php echo e($product->quantity_in_stock); ?>

                                    </span>
                                    <br><small class="text-muted">الحد الأدنى: <?php echo e($product->minimum_stock_level); ?></small>
                                    <?php if($product->quantity_in_stock <= $product->minimum_stock_level): ?>
                                        <br><small class="text-danger"><i class="fas fa-exclamation-triangle"></i> مخزون منخفض</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($product->status == 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('products.show', $product->id)); ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('products.edit', $product->id)); ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-success btn-sm" title="تحديث المخزون" 
                                                data-toggle="modal" data-target="#stockModal<?php echo e($product->id); ?>">
                                            <i class="fas fa-boxes"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('<?php echo e(route('products.destroy', $product->id)); ?>', 'حذف المنتج', 'هل أنت متأكد من حذف هذا المنتج؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- Stock Update Modal -->
                            <div class="modal fade" id="stockModal<?php echo e($product->id); ?>" tabindex="-1" role="dialog">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تحديث مخزون: <?php echo e($product->name); ?></h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <form action="<?php echo e(route('products.update-stock', $product->id)); ?>" method="POST">
                                            <?php echo csrf_field(); ?>
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label>المخزون الحالي</label>
                                                    <input type="text" class="form-control" value="<?php echo e($product->quantity_in_stock); ?>" readonly>
                                                </div>
                                                <div class="form-group">
                                                    <label for="type">نوع العملية</label>
                                                    <select class="form-control" name="type" required>
                                                        <option value="add">إضافة</option>
                                                        <option value="subtract">خصم</option>
                                                        <option value="set">تعيين</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="quantity">الكمية</label>
                                                    <input type="number" class="form-control" name="quantity" min="0" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="reason">السبب</label>
                                                    <input type="text" class="form-control" name="reason" placeholder="سبب التحديث (اختياري)">
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                                <button type="submit" class="btn btn-primary">تحديث</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="9" class="text-center">لا توجد منتجات</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($products->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($products->appends(request()->query())->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($products->total()); ?></h3>
                <p>إجمالي المنتجات</p>
            </div>
            <div class="icon">
                <i class="fas fa-boxes"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($products->where('status', 'active')->count()); ?></h3>
                <p>المنتجات النشطة</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($products->filter(function($product) { return $product->quantity_in_stock <= $product->minimum_stock_level; })->count()); ?></h3>
                <p>مخزون منخفض</p>
            </div>
            <div class="icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($products->where('quantity_in_stock', 0)->count()); ?></h3>
                <p>نفد المخزون</p>
            </div>
            <div class="icon">
                <i class="fas fa-times-circle"></i>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    console.log('Products page loaded with notification system');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/products/index.blade.php ENDPATH**/ ?>