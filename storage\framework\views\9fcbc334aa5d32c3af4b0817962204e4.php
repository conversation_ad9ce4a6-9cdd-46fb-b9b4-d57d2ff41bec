<?php $__env->startSection('title', 'الأدوار والصلاحيات'); ?>
<?php $__env->startSection('page-title', 'إدارة الأدوار والصلاحيات'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">الأدوار والصلاحيات</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة الأدوار</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('roles.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> دور جديد
                    </a>
                    <a href="<?php echo e(route('users.index')); ?>" class="btn btn-info">
                        <i class="fas fa-users"></i> إدارة المستخدمين
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>اسم الدور</th>
                                <th>الاسم المعروض</th>
                                <th>الوصف</th>
                                <th>عدد المستخدمين</th>
                                <th>عدد الصلاحيات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <code><?php echo e($role->name); ?></code>
                                    <?php if(in_array($role->name, ['super-admin', 'admin'])): ?>
                                        <span class="badge badge-warning badge-sm">نظام</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo e($role->display_name ?? $role->name); ?></strong>
                                </td>
                                <td><?php echo e(Str::limit($role->description, 50) ?: '-'); ?></td>
                                <td>
                                    <span class="badge badge-primary"><?php echo e($role->users_count); ?></span>
                                </td>
                                <td>
                                    <span class="badge badge-info"><?php echo e($role->permissions_count); ?></span>
                                </td>
                                <td><?php echo e($role->created_at->format('Y-m-d')); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('roles.show', $role->id)); ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('roles.edit', $role->id)); ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if(!in_array($role->name, ['super-admin', 'admin', 'user']) && $role->users_count == 0): ?>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('<?php echo e(route('roles.destroy', $role->id)); ?>', 'حذف الدور', 'هل أنت متأكد من حذف هذا الدور؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center">لا توجد أدوار</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($roles->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($roles->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($roles->total()); ?></h3>
                <p>إجمالي الأدوار</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-tag"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($roles->where('users_count', '>', 0)->count()); ?></h3>
                <p>أدوار مُستخدمة</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($roles->sum('users_count')); ?></h3>
                <p>إجمالي التعيينات</p>
            </div>
            <div class="icon">
                <i class="fas fa-link"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($roles->where('users_count', 0)->count()); ?></h3>
                <p>أدوار غير مُستخدمة</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-slash"></i>
            </div>
        </div>
    </div>
</div>

<!-- System Roles Info -->
<div class="row">
    <div class="col-12">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">معلومات الأدوار</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-danger"><i class="fas fa-crown"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Super Admin</span>
                                <span class="info-box-number">جميع الصلاحيات</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-warning"><i class="fas fa-user-shield"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Admin</span>
                                <span class="info-box-number">صلاحيات إدارية</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-user"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">User</span>
                                <span class="info-box-number">صلاحيات أساسية</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="alert alert-info">
                    <h5><i class="icon fas fa-info"></i> ملاحظة!</h5>
                    الأدوار المميزة بـ "نظام" هي أدوار أساسية ولا يمكن حذفها. يمكن تعديل صلاحياتها ولكن لا يُنصح بذلك.
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    console.log('Roles page loaded with notification system');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/roles/index.blade.php ENDPATH**/ ?>