<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Sales\Sale;
use App\Models\Sales\Customer;
use App\Models\Inventory\Product;
use Illuminate\Http\Request;

class SalesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $sales = Sale::with(['customer', 'user'])
            ->latest()
            ->paginate(15);

        return view('sales.index', compact('sales'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::where('status', 'active')->get();
        $products = Product::where('status', 'active')->get();

        return view('sales.create', compact('customers', 'products'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'total_amount' => 'required|numeric|min:0',
                'status' => 'required|in:pending,confirmed,cancelled'
            ]);

            // Create sale
            $sale = Sale::create([
                'customer_id' => $request->customer_id,
                'user_id' => auth()->id(),
                'total_amount' => $request->total_amount,
                'status' => $request->status,
                'sale_date' => now(),
                'reference_number' => generate_reference_number('SALE')
            ]);

            // Success notification
            notify_crud('created', 'sale');

            return redirect()->route('sales.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'sale', false);

            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $sale = Sale::findOrFail($id);

            // Validation
            $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'total_amount' => 'required|numeric|min:0',
                'status' => 'required|in:pending,confirmed,cancelled'
            ]);

            // Update sale
            $sale->update([
                'customer_id' => $request->customer_id,
                'total_amount' => $request->total_amount,
                'status' => $request->status
            ]);

            // Success notification
            notify_crud('updated', 'sale');

            return redirect()->route('sales.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'sale', false);

            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $sale = Sale::findOrFail($id);
            $sale->delete();

            // Success notification
            notify_crud('deleted', 'sale');

            return redirect()->route('sales.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'sale', false);

            return back();
        }
    }
}
