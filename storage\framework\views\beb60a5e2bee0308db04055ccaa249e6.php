<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="<?php echo e(route('dashboard')); ?>" class="brand-link">
        <img src="<?php echo e(asset('dist/img/AdminLTELogo.png')); ?>" alt="OmniFlow Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">OmniFlow ERP</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar user panel (optional) -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
            <div class="image">
                <img src="<?php echo e(asset('dist/img/user2-160x160.jpg')); ?>" class="img-circle elevation-2" alt="User Image">
            </div>
            <div class="info">
                <a href="<?php echo e(route('profile.edit')); ?>" class="d-block"><?php echo e(Auth::user()->name); ?></a>
                <small class="text-muted"><?php echo e(Auth::user()->email); ?></small>
            </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">

                <!-- Dashboard -->
                <li class="nav-item">
                    <a href="<?php echo e(route('dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>لوحة التحكم</p>
                    </a>
                </li>

                <!-- Sales Module -->
                <li class="nav-item has-treeview <?php echo e(request()->routeIs('sales.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('sales.*') ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-shopping-cart"></i>
                        <p>
                            المبيعات
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<?php echo e(route('sales.index')); ?>" class="nav-link <?php echo e(request()->routeIs('sales.index') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>جميع المبيعات</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('sales.create')); ?>" class="nav-link <?php echo e(request()->routeIs('sales.create') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>مبيعة جديدة</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('customers.index')); ?>" class="nav-link <?php echo e(request()->routeIs('customers.*') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>العملاء</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('reports.sales')); ?>" class="nav-link <?php echo e(request()->routeIs('reports.sales') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>تقارير المبيعات</p>
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Inventory Module -->
                <li class="nav-item has-treeview <?php echo e(request()->routeIs('inventory.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('inventory.*') ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-boxes"></i>
                        <p>
                            المخزون
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<?php echo e(route('inventory.index')); ?>" class="nav-link <?php echo e(request()->routeIs('inventory.index') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>جميع المنتجات</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('inventory.create')); ?>" class="nav-link <?php echo e(request()->routeIs('inventory.create') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>منتج جديد</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('categories.index')); ?>" class="nav-link <?php echo e(request()->routeIs('categories.*') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>التصنيفات</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('suppliers.index')); ?>" class="nav-link <?php echo e(request()->routeIs('suppliers.*') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>الموردين</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('reports.inventory')); ?>" class="nav-link <?php echo e(request()->routeIs('reports.inventory') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>حركة المخزون</p>
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Accounting Module -->
                <li class="nav-item has-treeview <?php echo e(request()->routeIs('accounting.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('accounting.*') ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-calculator"></i>
                        <p>
                            المحاسبة
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<?php echo e(route('accounting.index')); ?>" class="nav-link <?php echo e(request()->routeIs('accounting.index') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>لوحة المحاسبة</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('accounts.index')); ?>" class="nav-link <?php echo e(request()->routeIs('accounts.*') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>الحسابات</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('transactions.index')); ?>" class="nav-link <?php echo e(request()->routeIs('transactions.*') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>المعاملات</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('reports.financial')); ?>" class="nav-link <?php echo e(request()->routeIs('reports.financial') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>التقارير المالية</p>
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Reports -->
                <li class="nav-item has-treeview">
                    <a href="#" class="nav-link">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <p>
                            التقارير
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<?php echo e(route('reports.index')); ?>" class="nav-link <?php echo e(request()->routeIs('reports.index') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>جميع التقارير</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('reports.sales')); ?>" class="nav-link <?php echo e(request()->routeIs('reports.sales') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>تقارير المبيعات</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('reports.inventory')); ?>" class="nav-link <?php echo e(request()->routeIs('reports.inventory') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>تقارير المخزون</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('reports.financial')); ?>" class="nav-link <?php echo e(request()->routeIs('reports.financial') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>التقارير المالية</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('reports.customers')); ?>" class="nav-link <?php echo e(request()->routeIs('reports.customers') ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>تقارير العملاء</p>
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Settings -->
                <li class="nav-header">الإعدادات</li>
                <li class="nav-item">
                    <a href="<?php echo e(route('users.index')); ?>" class="nav-link <?php echo e(request()->routeIs('users.*') || request()->routeIs('roles.*') ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-users"></i>
                        <p>المستخدمين</p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo e(route('settings.index')); ?>" class="nav-link <?php echo e(request()->routeIs('settings.*') ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-cog"></i>
                        <p>إعدادات النظام</p>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</aside>
<?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/layouts/partials/sidebar.blade.php ENDPATH**/ ?>