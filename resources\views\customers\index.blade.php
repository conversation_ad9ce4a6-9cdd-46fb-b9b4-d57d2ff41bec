@extends('layouts.app')

@section('title', 'العملاء')
@section('page-title', 'إدارة العملاء')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">العملاء</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة العملاء</h3>
                <div class="card-tools">
                    <a href="{{ route('customers.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> عميل جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>المدينة</th>
                                <th>نوع العميل</th>
                                <th>عدد المبيعات</th>
                                <th>إجمالي المبيعات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($customers as $customer)
                            <tr>
                                <td>
                                    <strong>{{ $customer->name }}</strong>
                                    @if($customer->tax_number)
                                        <br><small class="text-muted">ض.ر: {{ $customer->tax_number }}</small>
                                    @endif
                                </td>
                                <td>{{ $customer->email ?: '-' }}</td>
                                <td>{{ $customer->phone }}</td>
                                <td>{{ $customer->city ?: '-' }}</td>
                                <td>
                                    @if($customer->customer_type == 'company')
                                        <span class="badge badge-info">شركة</span>
                                    @else
                                        <span class="badge badge-secondary">فرد</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge-primary">{{ $customer->sales_count }}</span>
                                </td>
                                <td>
                                    <strong>{{ number_format($customer->getTotalSalesAmount(), 2) }} ريال</strong>
                                </td>
                                <td>
                                    @if($customer->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @else
                                        <span class="badge badge-danger">غير نشط</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('customers.show', $customer->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('customers.edit', $customer->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('{{ route('customers.destroy', $customer->id) }}', 'حذف العميل', 'هل أنت متأكد من حذف هذا العميل؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center">لا توجد عملاء</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($customers->hasPages())
            <div class="card-footer">
                {{ $customers->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $customers->total() }}</h3>
                <p>إجمالي العملاء</p>
            </div>
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $customers->where('status', 'active')->count() }}</h3>
                <p>العملاء النشطين</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-check"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $customers->where('customer_type', 'company')->count() }}</h3>
                <p>الشركات</p>
            </div>
            <div class="icon">
                <i class="fas fa-building"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $customers->where('status', 'inactive')->count() }}</h3>
                <p>غير نشط</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-times"></i>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    console.log('Customers page loaded with notification system');
});
</script>
@endpush
