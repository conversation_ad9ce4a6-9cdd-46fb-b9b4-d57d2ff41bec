# 🔌 API Documentation - OmniFlow ERP 2025

## 📋 Overview

OmniFlow ERP 2025 provides a comprehensive RESTful API built with Laravel Sanctum for authentication. The API follows REST conventions and returns JSON responses.

### Base Information
- **Base URL:** `http://localhost:8000/api/v1`
- **Authentication:** <PERSON><PERSON> (Laravel Sanctum)
- **Content-Type:** `application/json`
- **Response Format:** JSON
- **API Version:** v1

---

## 🔐 Authentication

### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "Super Admin",
      "email": "<EMAIL>",
      "roles": ["Super Admin"]
    },
    "token": "1|abc123def456...",
    "token_type": "Bearer"
  },
  "message": "تم تسجيل الدخول بنجاح"
}
```

### Logout
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
```

### User Profile
```http
GET /api/v1/auth/user
Authorization: Bearer {token}
```

---

## 💼 Sales API

### Get All Sales
```http
GET /api/v1/sales
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (int): Page number for pagination
- `per_page` (int): Items per page (default: 15)
- `search` (string): Search in customer name or reference number
- `status` (string): Filter by status (pending, confirmed, delivered, cancelled)
- `payment_status` (string): Filter by payment status (pending, partial, paid)
- `date_from` (date): Filter from date (Y-m-d)
- `date_to` (date): Filter to date (Y-m-d)

**Response:**
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "customer_id": 1,
        "customer": {
          "id": 1,
          "name": "أحمد محمد علي",
          "email": "<EMAIL>"
        },
        "sale_date": "2025-06-17",
        "total_amount": "3047.50",
        "tax_amount": "397.50",
        "discount_amount": "0.00",
        "net_amount": "2650.00",
        "status": "delivered",
        "payment_status": "paid",
        "payment_method": "cash",
        "reference_number": "SAL-********-001",
        "notes": "بيع لابتوب وماوس للعميل أحمد",
        "created_at": "2025-06-20T12:00:00.000000Z",
        "updated_at": "2025-06-20T12:00:00.000000Z"
      }
    ],
    "first_page_url": "http://localhost:8000/api/v1/sales?page=1",
    "from": 1,
    "last_page": 1,
    "last_page_url": "http://localhost:8000/api/v1/sales?page=1",
    "next_page_url": null,
    "path": "http://localhost:8000/api/v1/sales",
    "per_page": 15,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

### Get Single Sale
```http
GET /api/v1/sales/{id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "customer": {
      "id": 1,
      "name": "أحمد محمد علي",
      "email": "<EMAIL>",
      "phone": "+966501234567"
    },
    "sale_items": [
      {
        "id": 1,
        "product": {
          "id": 1,
          "name": "لابتوب Dell Inspiron 15",
          "sku": "DELL-INS-15-001"
        },
        "quantity": 1,
        "unit_price": "2500.00",
        "total_price": "2500.00"
      }
    ],
    "sale_date": "2025-06-17",
    "total_amount": "3047.50",
    "status": "delivered",
    "payment_status": "paid"
  }
}
```

### Create Sale
```http
POST /api/v1/sales
Authorization: Bearer {token}
Content-Type: application/json

{
  "customer_id": 1,
  "sale_date": "2025-06-20",
  "items": [
    {
      "product_id": 1,
      "quantity": 2,
      "unit_price": 2500.00
    }
  ],
  "tax_rate": 15,
  "discount_amount": 0,
  "payment_method": "cash",
  "notes": "عملية بيع جديدة"
}
```

### Update Sale
```http
PUT /api/v1/sales/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "delivered",
  "payment_status": "paid",
  "notes": "تم التسليم بنجاح"
}
```

### Delete Sale
```http
DELETE /api/v1/sales/{id}
Authorization: Bearer {token}
```

### Sales Statistics
```http
GET /api/v1/sales/statistics/overview
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_sales": 1,
    "today_sales": 0,
    "monthly_sales": 1,
    "total_amount": "3047.50",
    "monthly_amount": "3047.50",
    "average_sale": "3047.50",
    "top_customers": [
      {
        "customer_name": "أحمد محمد علي",
        "total_purchases": "3047.50",
        "sales_count": 1
      }
    ]
  }
}
```

---

## 📦 Inventory API

### Get All Products
```http
GET /api/v1/inventory
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (int): Page number
- `per_page` (int): Items per page
- `search` (string): Search in name, SKU, or barcode
- `category_id` (int): Filter by category
- `supplier_id` (int): Filter by supplier
- `status` (string): Filter by status (active, inactive)
- `stock_status` (string): Filter by stock (in_stock, low_stock, out_of_stock)

**Response:**
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "لابتوب Dell Inspiron 15",
        "description": "لابتوب Dell Inspiron 15 بمعالج Intel Core i5",
        "sku": "DELL-INS-15-001",
        "barcode": "1234567890123",
        "price": "2500.00",
        "cost_price": "2000.00",
        "quantity_in_stock": 25,
        "minimum_stock_level": 5,
        "status": "active",
        "category": {
          "id": 1,
          "name": "إلكترونيات"
        },
        "supplier": {
          "id": 1,
          "name": "محمد أحمد التجاري"
        },
        "stock_status": "in_stock",
        "profit_margin": 20.0,
        "created_at": "2025-06-20T12:00:00.000000Z"
      }
    ],
    "total": 3
  }
}
```

### Get Single Product
```http
GET /api/v1/inventory/{id}
Authorization: Bearer {token}
```

### Create Product
```http
POST /api/v1/inventory
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "منتج جديد",
  "description": "وصف المنتج",
  "sku": "NEW-PROD-001",
  "barcode": "9876543210987",
  "category_id": 1,
  "supplier_id": 1,
  "price": 1500.00,
  "cost_price": 1200.00,
  "quantity_in_stock": 50,
  "minimum_stock_level": 10,
  "status": "active"
}
```

### Update Product Stock
```http
PUT /api/v1/inventory/{id}/stock
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "add",
  "quantity": 10,
  "reason": "استلام شحنة جديدة"
}
```

**Stock Update Types:**
- `add`: إضافة للمخزون
- `subtract`: خصم من المخزون  
- `set`: تعيين كمية محددة

### Get Low Stock Products
```http
GET /api/v1/inventory/low-stock
Authorization: Bearer {token}
```

---

## 💰 Accounting API

### Get All Transactions
```http
GET /api/v1/accounting/transactions
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (int): Page number
- `account_id` (int): Filter by account
- `type` (string): Filter by type (income, expense)
- `status` (string): Filter by status (pending, completed, cancelled)
- `date_from` (date): From date
- `date_to` (date): To date

**Response:**
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "account": {
          "id": 1,
          "name": "الصندوق",
          "code": "AST-CAS-001",
          "type": "asset"
        },
        "type": "income",
        "amount": "5000.00",
        "description": "إيداع نقدي ابتدائي",
        "transaction_date": "2025-06-10",
        "payment_method": "cash",
        "status": "completed",
        "reference_number": "INC-********-001",
        "created_at": "2025-06-20T12:00:00.000000Z"
      }
    ],
    "total": 2
  }
}
```

### Create Transaction
```http
POST /api/v1/accounting/transactions
Authorization: Bearer {token}
Content-Type: application/json

{
  "account_id": 1,
  "type": "income",
  "amount": 1000.00,
  "description": "إيداع نقدي",
  "transaction_date": "2025-06-20",
  "payment_method": "cash"
}
```

### Get Account Balances
```http
GET /api/v1/accounting/balances
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_assets": "200000.00",
    "total_liabilities": "0.00",
    "total_equity": "200000.00",
    "accounts": [
      {
        "id": 1,
        "name": "الصندوق",
        "type": "asset",
        "balance": "50000.00",
        "currency": "SAR"
      },
      {
        "id": 2,
        "name": "البنك الأهلي",
        "type": "asset", 
        "balance": "150000.00",
        "currency": "SAR"
      }
    ]
  }
}
```

### Get Financial Reports
```http
GET /api/v1/accounting/reports
Authorization: Bearer {token}
```

**Query Parameters:**
- `report_type` (string): Type of report (balance_sheet, income_statement, cash_flow)
- `date_from` (date): Start date
- `date_to` (date): End date

---

## 📊 Common Response Patterns

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "العملية تمت بنجاح"
}
```

### Error Response
```json
{
  "success": false,
  "message": "رسالة الخطأ",
  "errors": {
    "field_name": ["رسالة التحقق"]
  }
}
```

### Validation Error (422)
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "email": ["حقل البريد الإلكتروني مطلوب"],
    "password": ["كلمة المرور يجب أن تكون 8 أحرف على الأقل"]
  }
}
```

### Unauthorized (401)
```json
{
  "message": "Unauthenticated."
}
```

### Forbidden (403)
```json
{
  "message": "This action is unauthorized."
}
```

### Not Found (404)
```json
{
  "message": "Resource not found."
}
```

---

## 🔧 Rate Limiting

The API implements rate limiting to prevent abuse:

- **Authenticated requests:** 60 requests per minute
- **Guest requests:** 10 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
```

---

## 📝 Request/Response Examples

### Complete Sale Creation Example

**Request:**
```http
POST /api/v1/sales
Authorization: Bearer 1|abc123def456...
Content-Type: application/json

{
  "customer_id": 1,
  "sale_date": "2025-06-20",
  "items": [
    {
      "product_id": 1,
      "quantity": 1,
      "unit_price": 2500.00
    },
    {
      "product_id": 2,
      "quantity": 2,
      "unit_price": 150.00
    }
  ],
  "tax_rate": 15,
  "discount_amount": 100.00,
  "payment_method": "bank_transfer",
  "notes": "طلب خاص للعميل"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "customer_id": 1,
    "sale_date": "2025-06-20",
    "subtotal": "2800.00",
    "tax_amount": "405.00",
    "discount_amount": "100.00",
    "total_amount": "3105.00",
    "net_amount": "2700.00",
    "status": "pending",
    "payment_status": "pending",
    "payment_method": "bank_transfer",
    "reference_number": "SAL-********-002",
    "notes": "طلب خاص للعميل",
    "items": [
      {
        "product_id": 1,
        "quantity": 1,
        "unit_price": "2500.00",
        "total_price": "2500.00"
      },
      {
        "product_id": 2,
        "quantity": 2,
        "unit_price": "150.00",
        "total_price": "300.00"
      }
    ],
    "created_at": "2025-06-20T15:30:00.000000Z"
  },
  "message": "تم إنشاء المبيعة بنجاح"
}
```

---

## 🚀 API Status

### Current Implementation Status
- ✅ **Authentication API** - Complete with Sanctum
- ✅ **Sales API** - Full CRUD + Statistics
- ✅ **Inventory API** - Full CRUD + Stock Management
- ✅ **Accounting API** - Transactions + Reports
- ✅ **Error Handling** - Comprehensive error responses
- ✅ **Validation** - Request validation for all endpoints
- ✅ **Rate Limiting** - Protection against abuse
- ✅ **Documentation** - Complete API documentation

### Ready for Production Use! 🎉
