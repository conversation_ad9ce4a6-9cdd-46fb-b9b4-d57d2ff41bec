<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Product;
use App\Models\Inventory\Category;
use App\Models\Inventory\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::with(['category', 'supplier']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low_stock':
                    $query->whereColumn('quantity_in_stock', '<=', 'minimum_stock_level');
                    break;
                case 'out_of_stock':
                    $query->where('quantity_in_stock', 0);
                    break;
                case 'in_stock':
                    $query->where('quantity_in_stock', '>', 0);
                    break;
            }
        }

        $products = $query->latest()->paginate(15);

        // Get filter options
        $categories = Category::active()->orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();

        return view('products.index', compact('products', 'categories', 'suppliers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::active()->orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();

        return view('products.create', compact('categories', 'suppliers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'sku' => 'required|string|max:100|unique:products,sku',
                'barcode' => 'nullable|string|max:100|unique:products,barcode',
                'category_id' => 'required|exists:categories,id',
                'supplier_id' => 'required|exists:suppliers,id',
                'price' => 'required|numeric|min:0',
                'cost_price' => 'required|numeric|min:0',
                'quantity_in_stock' => 'required|integer|min:0',
                'minimum_stock_level' => 'required|integer|min:0',
                'weight' => 'nullable|numeric|min:0',
                'dimensions' => 'nullable|string|max:100',
                'status' => 'required|in:active,inactive',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $data = $request->all();

            // Set selling_price same as price for compatibility
            $data['selling_price'] = $data['price'];
            $data['unit_price'] = $data['price'];

            // Handle image upload
            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('products', 'public');
                $data['image'] = $imagePath;
            }

            // Create product
            Product::create($data);

            // Success notification
            notify_crud('created', 'product');

            return redirect()->route('products.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'product', false);

            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load(['category', 'supplier']);

        return view('products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $categories = Category::active()->orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();

        return view('products.edit', compact('product', 'categories', 'suppliers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'sku' => 'required|string|max:100|unique:products,sku,' . $product->id,
                'barcode' => 'nullable|string|max:100|unique:products,barcode,' . $product->id,
                'category_id' => 'required|exists:categories,id',
                'supplier_id' => 'required|exists:suppliers,id',
                'price' => 'required|numeric|min:0',
                'cost_price' => 'required|numeric|min:0',
                'quantity_in_stock' => 'required|integer|min:0',
                'minimum_stock_level' => 'required|integer|min:0',
                'weight' => 'nullable|numeric|min:0',
                'dimensions' => 'nullable|string|max:100',
                'status' => 'required|in:active,inactive',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $data = $request->all();

            // Set selling_price same as price for compatibility
            $data['selling_price'] = $data['price'];
            $data['unit_price'] = $data['price'];

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($product->image && Storage::disk('public')->exists($product->image)) {
                    Storage::disk('public')->delete($product->image);
                }

                $imagePath = $request->file('image')->store('products', 'public');
                $data['image'] = $imagePath;
            }

            // Update product
            $product->update($data);

            // Success notification
            notify_crud('updated', 'product');

            return redirect()->route('products.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'product', false);

            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        try {
            // Check if product is used in sales
            if (method_exists($product, 'saleItems') && $product->saleItems()->count() > 0) {
                notify_error('لا يمكن حذف المنتج لأنه مستخدم في مبيعات', 'خطأ في الحذف');
                return back();
            }

            // Delete image
            if ($product->image && Storage::disk('public')->exists($product->image)) {
                Storage::disk('public')->delete($product->image);
            }

            $product->delete();

            // Success notification
            notify_crud('deleted', 'product');

            return redirect()->route('products.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'product', false);

            return back();
        }
    }

    /**
     * Update product stock.
     */
    public function updateStock(Request $request, Product $product)
    {
        try {
            $request->validate([
                'quantity' => 'required|integer',
                'type' => 'required|in:add,subtract,set',
                'reason' => 'nullable|string|max:255'
            ]);

            $oldQuantity = $product->quantity_in_stock;

            switch ($request->type) {
                case 'add':
                    $product->increment('quantity_in_stock', $request->quantity);
                    break;
                case 'subtract':
                    $product->decrement('quantity_in_stock', $request->quantity);
                    break;
                case 'set':
                    $product->update(['quantity_in_stock' => $request->quantity]);
                    break;
            }

            $newQuantity = $product->fresh()->quantity_in_stock;

            notify_success("تم تحديث المخزون من {$oldQuantity} إلى {$newQuantity}", 'تحديث المخزون');

            return back();

        } catch (\Exception $e) {
            notify_error('فشل في تحديث المخزون', 'خطأ');
            return back();
        }
    }

    /**
     * Get low stock products.
     */
    public function lowStock()
    {
        $products = Product::with(['category', 'supplier'])
            ->whereColumn('quantity_in_stock', '<=', 'minimum_stock_level')
            ->orderBy('quantity_in_stock')
            ->paginate(15);

        return view('products.low-stock', compact('products'));
    }
}
