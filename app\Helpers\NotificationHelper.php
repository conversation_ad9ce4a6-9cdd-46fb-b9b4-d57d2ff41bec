<?php

namespace App\Helpers;

class NotificationHelper
{
    /**
     * Add success notification to session
     */
    public static function success($message, $title = null)
    {
        session()->flash('notification', [
            'type' => 'success',
            'title' => $title ?: 'نجح!',
            'message' => $message,
            'icon' => 'fas fa-check-circle',
            'color' => '#28a745'
        ]);
    }

    /**
     * Add error notification to session
     */
    public static function error($message, $title = null)
    {
        session()->flash('notification', [
            'type' => 'error',
            'title' => $title ?: 'خطأ!',
            'message' => $message,
            'icon' => 'fas fa-times-circle',
            'color' => '#dc3545'
        ]);
    }

    /**
     * Add warning notification to session
     */
    public static function warning($message, $title = null)
    {
        session()->flash('notification', [
            'type' => 'warning',
            'title' => $title ?: 'تحذير!',
            'message' => $message,
            'icon' => 'fas fa-exclamation-triangle',
            'color' => '#ffc107'
        ]);
    }

    /**
     * Add info notification to session
     */
    public static function info($message, $title = null)
    {
        session()->flash('notification', [
            'type' => 'info',
            'title' => $title ?: 'معلومة!',
            'message' => $message,
            'icon' => 'fas fa-info-circle',
            'color' => '#17a2b8'
        ]);
    }

    /**
     * Add custom notification to session
     */
    public static function custom($type, $message, $title = null, $icon = null, $color = null)
    {
        session()->flash('notification', [
            'type' => $type,
            'title' => $title ?: 'إشعار',
            'message' => $message,
            'icon' => $icon ?: 'fas fa-bell',
            'color' => $color ?: '#6c757d'
        ]);
    }

    /**
     * Add CRUD operation notification
     */
    public static function crud($operation, $entity, $success = true)
    {
        $operations = [
            'created' => [
                'success' => ['title' => 'تم الإنشاء!', 'message' => 'تم إنشاء :entity بنجاح', 'icon' => 'fas fa-plus-circle'],
                'error' => ['title' => 'فشل الإنشاء!', 'message' => 'فشل في إنشاء :entity', 'icon' => 'fas fa-times-circle']
            ],
            'updated' => [
                'success' => ['title' => 'تم التحديث!', 'message' => 'تم تحديث :entity بنجاح', 'icon' => 'fas fa-edit'],
                'error' => ['title' => 'فشل التحديث!', 'message' => 'فشل في تحديث :entity', 'icon' => 'fas fa-times-circle']
            ],
            'deleted' => [
                'success' => ['title' => 'تم الحذف!', 'message' => 'تم حذف :entity بنجاح', 'icon' => 'fas fa-trash'],
                'error' => ['title' => 'فشل الحذف!', 'message' => 'فشل في حذف :entity', 'icon' => 'fas fa-times-circle']
            ],
            'restored' => [
                'success' => ['title' => 'تم الاستعادة!', 'message' => 'تم استعادة :entity بنجاح', 'icon' => 'fas fa-undo'],
                'error' => ['title' => 'فشل الاستعادة!', 'message' => 'فشل في استعادة :entity', 'icon' => 'fas fa-times-circle']
            ]
        ];

        $status = $success ? 'success' : 'error';
        $config = $operations[$operation][$status] ?? $operations['created'][$status];

        $message = str_replace(':entity', $entity, $config['message']);

        if ($success) {
            self::success($message, $config['title']);
        } else {
            self::error($message, $config['title']);
        }
    }

    /**
     * Get entity names in Arabic
     */
    public static function getEntityName($entity)
    {
        $entities = [
            'sale' => 'المبيعة',
            'sales' => 'المبيعات',
            'customer' => 'العميل',
            'customers' => 'العملاء',
            'product' => 'المنتج',
            'products' => 'المنتجات',
            'category' => 'الفئة',
            'categories' => 'الفئات',
            'supplier' => 'المورد',
            'suppliers' => 'الموردين',
            'account' => 'الحساب',
            'accounts' => 'الحسابات',
            'transaction' => 'المعاملة',
            'transactions' => 'المعاملات',
            'user' => 'المستخدم',
            'users' => 'المستخدمين',
            'role' => 'الدور',
            'roles' => 'الأدوار',
            'permission' => 'الصلاحية',
            'permissions' => 'الصلاحيات',
        ];

        return $entities[strtolower($entity)] ?? $entity;
    }
}
