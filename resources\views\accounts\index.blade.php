@extends('layouts.app')

@section('title', 'الحسابات المحاسبية')
@section('page-title', 'إدارة الحسابات المحاسبية')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="{{ route('accounting.index') }}">المحاسبة</a></li>
    <li class="breadcrumb-item active">الحسابات</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة الحسابات المحاسبية</h3>
                <div class="card-tools">
                    <a href="{{ route('accounts.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> حساب جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم الحساب</th>
                                <th>نوع الحساب</th>
                                <th>الرصيد</th>
                                <th>العملة</th>
                                <th>عدد المعاملات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($accounts as $account)
                            <tr>
                                <td>
                                    <code>{{ $account->code }}</code>
                                    @if($account->is_default)
                                        <span class="badge badge-warning badge-sm">افتراضي</span>
                                    @endif
                                </td>
                                <td>
                                    <strong>{{ $account->name }}</strong>
                                    @if($account->description)
                                        <br><small class="text-muted">{{ Str::limit($account->description, 50) }}</small>
                                    @endif
                                    @if($account->isBankAccount())
                                        <br><small class="text-info">
                                            <i class="fas fa-university"></i> {{ $account->bank_name }}
                                            @if($account->account_number)
                                                - {{ $account->masked_account_number }}
                                            @endif
                                        </small>
                                    @endif
                                </td>
                                <td>
                                    @switch($account->type)
                                        @case('asset')
                                            <span class="badge badge-success">أصول</span>
                                            @break
                                        @case('liability')
                                            <span class="badge badge-danger">التزامات</span>
                                            @break
                                        @case('equity')
                                            <span class="badge badge-primary">حقوق ملكية</span>
                                            @break
                                        @case('revenue')
                                            <span class="badge badge-info">إيرادات</span>
                                            @break
                                        @case('expense')
                                            <span class="badge badge-warning">مصروفات</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ $account->type }}</span>
                                    @endswitch
                                </td>
                                <td>
                                    <strong class="{{ $account->balance >= 0 ? 'text-success' : 'text-danger' }}">
                                        {{ number_format($account->balance, 2) }}
                                    </strong>
                                </td>
                                <td>
                                    <span class="badge badge-light">{{ $account->currency }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-primary">{{ $account->transactions_count }}</span>
                                </td>
                                <td>
                                    @if($account->status == 'active')
                                        <span class="badge badge-success">نشط</span>
                                    @else
                                        <span class="badge badge-danger">غير نشط</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('accounts.show', $account->id) }}" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('accounts.edit', $account->id) }}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if(!$account->is_default && $account->transactions_count == 0)
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('{{ route('accounts.destroy', $account->id) }}', 'حذف الحساب', 'هل أنت متأكد من حذف هذا الحساب؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">لا توجد حسابات</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($accounts->hasPages())
            <div class="card-footer">
                {{ $accounts->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Account Summary Cards -->
<div class="row">
    <div class="col-lg-2 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $accounts->where('type', 'asset')->count() }}</h3>
                <p>الأصول</p>
            </div>
            <div class="icon">
                <i class="fas fa-coins"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $accounts->where('type', 'liability')->count() }}</h3>
                <p>الالتزامات</p>
            </div>
            <div class="icon">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-primary">
            <div class="inner">
                <h3>{{ $accounts->where('type', 'equity')->count() }}</h3>
                <p>حقوق الملكية</p>
            </div>
            <div class="icon">
                <i class="fas fa-balance-scale"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $accounts->where('type', 'revenue')->count() }}</h3>
                <p>الإيرادات</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-up"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $accounts->where('type', 'expense')->count() }}</h3>
                <p>المصروفات</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-down"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-secondary">
            <div class="inner">
                <h3>{{ $accounts->where('status', 'active')->count() }}</h3>
                <p>النشطة</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    console.log('Accounts page loaded with notification system');
});
</script>
@endpush
