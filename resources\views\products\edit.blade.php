@extends('layouts.app')

@section('title', 'تعديل المنتج: ' . $product->name)
@section('page-title', 'تعديل المنتج')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="{{ route('products.index') }}">المنتجات</a></li>
    <li class="breadcrumb-item active">تعديل: {{ $product->name }}</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">تعديل بيانات المنتج</h3>
                <div class="card-tools">
                    <a href="{{ route('products.show', $product->id) }}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> عرض المنتج
                    </a>
                </div>
            </div>
            
            <form action="{{ route('products.update', $product->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="card-body">
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">اسم المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $product->name) }}" required>
                                @error('name')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sku">رمز المنتج (SKU) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sku') is-invalid @enderror" 
                                       id="sku" name="sku" value="{{ old('sku', $product->sku) }}" required>
                                @error('sku')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">وصف المنتج</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3">{{ old('description', $product->description) }}</textarea>
                                @error('description')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Category and Supplier -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="category_id">التصنيف <span class="text-danger">*</span></label>
                                <select class="form-control @error('category_id') is-invalid @enderror" 
                                        id="category_id" name="category_id" required>
                                    <option value="">اختر التصنيف</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" 
                                                {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="supplier_id">المورد <span class="text-danger">*</span></label>
                                <select class="form-control @error('supplier_id') is-invalid @enderror" 
                                        id="supplier_id" name="supplier_id" required>
                                    <option value="">اختر المورد</option>
                                    @foreach($suppliers as $supplier)
                                        <option value="{{ $supplier->id }}" 
                                                {{ old('supplier_id', $product->supplier_id) == $supplier->id ? 'selected' : '' }}>
                                            {{ $supplier->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('supplier_id')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="cost_price">سعر التكلفة <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" class="form-control @error('cost_price') is-invalid @enderror" 
                                       id="cost_price" name="cost_price" value="{{ old('cost_price', $product->cost_price) }}" required>
                                @error('cost_price')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="price">سعر البيع <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" class="form-control @error('price') is-invalid @enderror" 
                                       id="price" name="price" value="{{ old('price', $product->price ?? $product->selling_price) }}" required>
                                @error('price')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Stock Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quantity_in_stock">الكمية في المخزون <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('quantity_in_stock') is-invalid @enderror" 
                                       id="quantity_in_stock" name="quantity_in_stock" 
                                       value="{{ old('quantity_in_stock', $product->quantity_in_stock) }}" required>
                                @error('quantity_in_stock')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="minimum_stock_level">الحد الأدنى للمخزون <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('minimum_stock_level') is-invalid @enderror" 
                                       id="minimum_stock_level" name="minimum_stock_level" 
                                       value="{{ old('minimum_stock_level', $product->minimum_stock_level) }}" required>
                                @error('minimum_stock_level')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="barcode">الباركود</label>
                                <input type="text" class="form-control @error('barcode') is-invalid @enderror" 
                                       id="barcode" name="barcode" value="{{ old('barcode', $product->barcode) }}">
                                @error('barcode')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="weight">الوزن (كجم)</label>
                                <input type="number" step="0.01" class="form-control @error('weight') is-invalid @enderror" 
                                       id="weight" name="weight" value="{{ old('weight', $product->weight) }}">
                                @error('weight')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="dimensions">الأبعاد</label>
                                <input type="text" class="form-control @error('dimensions') is-invalid @enderror" 
                                       id="dimensions" name="dimensions" value="{{ old('dimensions', $product->dimensions) }}" 
                                       placeholder="مثال: 30x20x10 سم">
                                @error('dimensions')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Status and Image -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">الحالة <span class="text-danger">*</span></label>
                                <select class="form-control @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="active" {{ old('status', $product->status) == 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="inactive" {{ old('status', $product->status) == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                </select>
                                @error('status')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="image">صورة المنتج</label>
                                <input type="file" class="form-control-file @error('image') is-invalid @enderror" 
                                       id="image" name="image" accept="image/*">
                                @error('image')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                                <small class="form-text text-muted">الحد الأقصى: 2MB، الأنواع المدعومة: JPG, PNG, GIF</small>
                                
                                @if($product->image)
                                    <div class="mt-2">
                                        <label>الصورة الحالية:</label><br>
                                        <img src="{{ asset('storage/' . $product->image) }}" 
                                             alt="{{ $product->name }}" 
                                             class="img-thumbnail" 
                                             style="max-width: 150px; max-height: 150px;">
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Current Product Info -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> معلومات المنتج الحالية:</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>المخزون الحالي:</strong> {{ $product->quantity_in_stock }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>آخر تحديث:</strong> {{ $product->updated_at->format('Y-m-d H:i') }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>تاريخ الإنشاء:</strong> {{ $product->created_at->format('Y-m-d H:i') }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>الحالة:</strong> 
                                        <span class="badge badge-{{ $product->status == 'active' ? 'success' : 'danger' }}">
                                            {{ $product->status == 'active' ? 'نشط' : 'غير نشط' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <button type="submit" class="btn btn-primary" id="updateBtn">
                        <i class="fas fa-save"></i> تحديث المنتج
                    </button>
                    <a href="{{ route('products.show', $product->id) }}" class="btn btn-info">
                        <i class="fas fa-eye"></i> عرض المنتج
                    </a>
                    <a href="{{ route('products.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission with loading
    $('form').on('submit', function(e) {
        const updateBtn = $('#updateBtn');
        updateBtn.prop('disabled', true);
        updateBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري التحديث...');
        
        showLoading('جاري تحديث بيانات المنتج...');
    });

    // Calculate profit margin
    $('#cost_price, #price').on('keyup', function() {
        const costPrice = parseFloat($('#cost_price').val()) || 0;
        const sellPrice = parseFloat($('#price').val()) || 0;
        
        if (costPrice > 0 && sellPrice > 0) {
            const profit = sellPrice - costPrice;
            const margin = ((profit / sellPrice) * 100).toFixed(2);
            
            // Show profit info
            console.log(`Profit: ${profit}, Margin: ${margin}%`);
            
            // You can add visual feedback here
            if (margin < 10) {
                $('#price').addClass('border-warning');
            } else if (margin > 30) {
                $('#price').addClass('border-success');
            } else {
                $('#price').removeClass('border-warning border-success');
            }
        }
    });

    // Image preview
    $('#image').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Remove existing preview
                $('.image-preview').remove();
                
                // Add new preview
                const preview = `
                    <div class="mt-2 image-preview">
                        <label>معاينة الصورة الجديدة:</label><br>
                        <img src="${e.target.result}" 
                             class="img-thumbnail" 
                             style="max-width: 150px; max-height: 150px;">
                    </div>
                `;
                $('#image').parent().append(preview);
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endpush
