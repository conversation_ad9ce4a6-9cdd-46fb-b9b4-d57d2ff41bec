<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Sales\Sale;
use App\Models\Sales\Customer;
use App\Models\Inventory\Supplier;
use App\Models\Inventory\Product;
use App\Models\Accounting\Account;
use App\Models\Accounting\Transaction;
use App\Models\User;

class DashboardController extends Controller
{
    public function index()
    {
        // Get comprehensive statistics
        $stats = [
            // Sales Statistics
            'total_sales' => Sale::count(),
            'sales_today' => Sale::whereDate('created_at', today())->count(),
            'monthly_sales' => Sale::whereMonth('created_at', now()->month)->count(),
            'sales_amount' => Sale::sum('total_amount'),
            'monthly_sales_amount' => Sale::whereMonth('created_at', now()->month)->sum('total_amount'),

            // Customer & Supplier Statistics
            'total_customers' => Customer::count(),
            'active_customers' => Customer::where('status', 'active')->count(),
            'total_suppliers' => Supplier::count(),
            'active_suppliers' => Supplier::where('status', 'active')->count(),

            // Inventory Statistics
            'total_products' => Product::count(),
            'low_stock_products' => Product::whereColumn('quantity_in_stock', '<=', 'minimum_stock_level')->count(),
            'out_of_stock_products' => Product::where('quantity_in_stock', 0)->count(),

            // Financial Statistics
            'total_accounts' => Account::count(),
            'active_accounts' => Account::where('status', 'active')->count(),
            'total_transactions' => Transaction::count(),
            'monthly_transactions' => Transaction::whereMonth('transaction_date', now()->month)->count(),

            // User Statistics
            'total_users' => User::count(),
            'active_users' => User::count(),
        ];

        // Get recent activities
        $recent_sales = Sale::with(['customer', 'user'])
            ->latest()
            ->take(5)
            ->get();

        $recent_transactions = Transaction::with(['account'])
            ->latest('transaction_date')
            ->take(5)
            ->get();

        // Get low stock products
        $low_stock_products = Product::with(['category'])
            ->whereColumn('quantity_in_stock', '<=', 'minimum_stock_level')
            ->take(5)
            ->get();

        // Monthly sales chart data
        $monthly_sales_data = Sale::selectRaw('MONTH(created_at) as month, SUM(total_amount) as total')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // System health check
        $system_health = [
            'database' => true,
            'storage' => true,
            'cache' => true
        ];

        return view('dashboard.index', compact(
            'stats',
            'recent_sales',
            'recent_transactions',
            'low_stock_products',
            'monthly_sales_data',
            'system_health'
        ));
    }
}
