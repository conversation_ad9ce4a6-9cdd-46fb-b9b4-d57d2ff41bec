<?php $__env->startSection('title', 'التصنيفات'); ?>
<?php $__env->startSection('page-title', 'إدارة التصنيفات'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">التصنيفات</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة التصنيفات</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('categories.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> تصنيف جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>التصنيف الأب</th>
                                <th>عدد المنتجات</th>
                                <th>ترتيب العرض</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <?php if($category->image): ?>
                                        <img src="<?php echo e(asset('storage/categories/' . $category->image)); ?>" 
                                             alt="<?php echo e($category->name); ?>" 
                                             class="img-thumbnail" 
                                             style="width: 50px; height: 50px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px; border-radius: 4px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo e($category->name); ?></strong>
                                    <?php if($category->description): ?>
                                        <br><small class="text-muted"><?php echo e(Str::limit($category->description, 50)); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($category->parent): ?>
                                        <span class="badge badge-info"><?php echo e($category->parent->name); ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">تصنيف رئيسي</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-primary"><?php echo e($category->products_count); ?></span>
                                    <?php if($category->children()->count() > 0): ?>
                                        <br><small class="text-muted"><?php echo e($category->children()->count()); ?> تصنيف فرعي</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-light"><?php echo e($category->sort_order); ?></span>
                                </td>
                                <td>
                                    <?php if($category->status == 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('categories.show', $category->id)); ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('categories.edit', $category->id)); ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('<?php echo e(route('categories.destroy', $category->id)); ?>', 'حذف التصنيف', 'هل أنت متأكد من حذف هذا التصنيف؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center">لا توجد تصنيفات</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($categories->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($categories->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($categories->total()); ?></h3>
                <p>إجمالي التصنيفات</p>
            </div>
            <div class="icon">
                <i class="fas fa-tags"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($categories->where('status', 'active')->count()); ?></h3>
                <p>التصنيفات النشطة</p>
            </div>
            <div class="icon">
                <i class="fas fa-tag"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($categories->whereNull('parent_id')->count()); ?></h3>
                <p>التصنيفات الرئيسية</p>
            </div>
            <div class="icon">
                <i class="fas fa-folder"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($categories->whereNotNull('parent_id')->count()); ?></h3>
                <p>التصنيفات الفرعية</p>
            </div>
            <div class="icon">
                <i class="fas fa-folder-open"></i>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    console.log('Categories page loaded with notification system');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/categories/index.blade.php ENDPATH**/ ?>