<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $roles = Role::withCount(['users', 'permissions'])
            ->latest()
            ->paginate(15);

        return view('roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $permissions = Permission::all()->groupBy('group');
        return view('roles.create', compact('permissions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255|unique:roles,name',
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'permissions' => 'nullable|array',
                'permissions.*' => 'exists:permissions,id'
            ]);

            // Create role
            $role = Role::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'guard_name' => 'web'
            ]);

            // Assign permissions
            if ($request->permissions) {
                $role->syncPermissions($request->permissions);
            }

            // Success notification
            notify_crud('created', 'role');

            return redirect()->route('roles.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'role', false);
            
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Role $role)
    {
        $role->load(['users', 'permissions']);
        
        $stats = [
            'users_count' => $role->users->count(),
            'permissions_count' => $role->permissions->count(),
            'created_at' => $role->created_at,
            'updated_at' => $role->updated_at
        ];

        $permissionsByGroup = $role->permissions->groupBy('group');

        return view('roles.show', compact('role', 'stats', 'permissionsByGroup'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Role $role)
    {
        $permissions = Permission::all()->groupBy('group');
        $rolePermissions = $role->permissions->pluck('id')->toArray();
        
        return view('roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Role $role)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'permissions' => 'nullable|array',
                'permissions.*' => 'exists:permissions,id'
            ]);

            // Update role
            $role->update([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description
            ]);

            // Sync permissions
            if ($request->has('permissions')) {
                $role->syncPermissions($request->permissions);
            } else {
                $role->syncPermissions([]);
            }

            // Success notification
            notify_crud('updated', 'role');

            return redirect()->route('roles.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'role', false);
            
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role)
    {
        try {
            // Check if role has users
            if ($role->users()->count() > 0) {
                notify_error('لا يمكن حذف الدور لأنه مُعيّن لمستخدمين', 'خطأ في الحذف');
                return back();
            }

            // Prevent deleting system roles
            $systemRoles = ['super-admin', 'admin', 'user'];
            if (in_array($role->name, $systemRoles)) {
                notify_error('لا يمكن حذف أدوار النظام الأساسية', 'خطأ في الحذف');
                return back();
            }

            $role->delete();

            // Success notification
            notify_crud('deleted', 'role');

            return redirect()->route('roles.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'role', false);
            
            return back();
        }
    }

    /**
     * Get role statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_roles' => Role::count(),
            'roles_with_users' => Role::whereHas('users')->count(),
            'roles_without_users' => Role::whereDoesntHave('users')->count(),
            'total_permissions' => Permission::count(),
            'most_used_role' => Role::withCount('users')->orderBy('users_count', 'desc')->first()?->name
        ];

        return response()->json($stats);
    }
}
