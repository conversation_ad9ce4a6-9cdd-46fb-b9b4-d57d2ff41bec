<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $users = User::with('roles')
            ->latest()
            ->paginate(15);

        return view('users.index', compact('users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::all();
        return view('users.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'password' => 'required|string|min:8|confirmed',
                'phone' => 'nullable|string|max:20',
                'department' => 'nullable|string|max:100',
                'position' => 'nullable|string|max:100',
                'status' => 'required|in:active,inactive',
                'roles' => 'nullable|array',
                'roles.*' => 'exists:roles,id'
            ]);

            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
                'department' => $request->department,
                'position' => $request->position,
                'status' => $request->status,
                'email_verified_at' => now()
            ]);

            // Assign roles
            if ($request->roles) {
                $user->assignRole($request->roles);
            }

            // Success notification
            notify_crud('created', 'user');

            return redirect()->route('users.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'user', false);
            
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load(['roles.permissions']);
        
        $stats = [
            'roles_count' => $user->roles->count(),
            'permissions_count' => $user->getAllPermissions()->count(),
            'created_sales' => $user->sales()->count() ?? 0,
            'created_transactions' => $user->transactions()->count() ?? 0
        ];

        return view('users.show', compact('user', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $userRoles = $user->roles->pluck('id')->toArray();
        
        return view('users.edit', compact('user', 'roles', 'userRoles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $user->id,
                'password' => 'nullable|string|min:8|confirmed',
                'phone' => 'nullable|string|max:20',
                'department' => 'nullable|string|max:100',
                'position' => 'nullable|string|max:100',
                'status' => 'required|in:active,inactive',
                'roles' => 'nullable|array',
                'roles.*' => 'exists:roles,id'
            ]);

            $data = [
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'department' => $request->department,
                'position' => $request->position,
                'status' => $request->status
            ];

            // Update password if provided
            if ($request->filled('password')) {
                $data['password'] = Hash::make($request->password);
            }

            // Update user
            $user->update($data);

            // Sync roles
            if ($request->has('roles')) {
                $user->syncRoles($request->roles);
            } else {
                $user->syncRoles([]);
            }

            // Success notification
            notify_crud('updated', 'user');

            return redirect()->route('users.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'user', false);
            
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        try {
            // Prevent deleting current user
            if ($user->id === auth()->id()) {
                notify_error('لا يمكنك حذف حسابك الخاص', 'خطأ في الحذف');
                return back();
            }

            // Check if user has related data
            $hasRelatedData = false;
            $relatedDataMessage = '';

            if (method_exists($user, 'sales') && $user->sales()->count() > 0) {
                $hasRelatedData = true;
                $relatedDataMessage .= 'مبيعات، ';
            }

            if (method_exists($user, 'transactions') && $user->transactions()->count() > 0) {
                $hasRelatedData = true;
                $relatedDataMessage .= 'معاملات مالية، ';
            }

            if ($hasRelatedData) {
                $relatedDataMessage = rtrim($relatedDataMessage, '، ');
                notify_error("لا يمكن حذف المستخدم لأنه مرتبط بـ: {$relatedDataMessage}", 'خطأ في الحذف');
                return back();
            }

            $user->delete();

            // Success notification
            notify_crud('deleted', 'user');

            return redirect()->route('users.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'user', false);
            
            return back();
        }
    }

    /**
     * Toggle user status.
     */
    public function toggleStatus(User $user)
    {
        try {
            $newStatus = $user->status === 'active' ? 'inactive' : 'active';
            $user->update(['status' => $newStatus]);

            $statusText = $newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل';
            notify_success("تم {$statusText} المستخدم بنجاح", 'تغيير الحالة');

            return back();
        } catch (\Exception $e) {
            notify_error('فشل في تغيير حالة المستخدم', 'خطأ');
            return back();
        }
    }

    /**
     * Get user statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'inactive_users' => User::where('status', 'inactive')->count(),
            'users_with_roles' => User::whereHas('roles')->count(),
            'users_without_roles' => User::whereDoesntHave('roles')->count(),
            'recent_users' => User::where('created_at', '>=', now()->subDays(30))->count()
        ];

        return response()->json($stats);
    }
}
