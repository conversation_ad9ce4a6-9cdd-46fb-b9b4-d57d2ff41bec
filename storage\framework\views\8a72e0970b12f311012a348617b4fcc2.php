<?php $__env->startSection('title', 'الموردين'); ?>
<?php $__env->startSection('page-title', 'إدارة الموردين'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item active">الموردين</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة الموردين</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('suppliers.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> مورد جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الاسم / الشركة</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>المدينة</th>
                                <th>نوع المورد</th>
                                <th>عدد المنتجات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <strong><?php echo e($supplier->company_name ?: $supplier->name); ?></strong>
                                    <?php if($supplier->company_name && $supplier->name): ?>
                                        <br><small class="text-muted"><?php echo e($supplier->name); ?></small>
                                    <?php endif; ?>
                                    <?php if($supplier->tax_number): ?>
                                        <br><small class="text-muted">ض.ر: <?php echo e($supplier->tax_number); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($supplier->email ?: '-'); ?></td>
                                <td>
                                    <?php echo e($supplier->phone); ?>

                                    <?php if($supplier->mobile): ?>
                                        <br><small class="text-muted"><?php echo e($supplier->mobile); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($supplier->city ?: '-'); ?></td>
                                <td>
                                    <?php switch($supplier->supplier_type):
                                        case ('company'): ?>
                                            <span class="badge badge-info">شركة</span>
                                            <?php break; ?>
                                        <?php case ('individual'): ?>
                                            <span class="badge badge-secondary">فرد</span>
                                            <?php break; ?>
                                        <?php case ('government'): ?>
                                            <span class="badge badge-warning">جهة حكومية</span>
                                            <?php break; ?>
                                        <?php case ('international'): ?>
                                            <span class="badge badge-primary">دولي</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-light"><?php echo e($supplier->supplier_type); ?></span>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <span class="badge badge-primary"><?php echo e($supplier->products_count); ?></span>
                                </td>
                                <td>
                                    <?php if($supplier->status == 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('suppliers.show', $supplier->id)); ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('suppliers.edit', $supplier->id)); ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('<?php echo e(route('suppliers.destroy', $supplier->id)); ?>', 'حذف المورد', 'هل أنت متأكد من حذف هذا المورد؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="text-center">لا توجد موردين</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($suppliers->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($suppliers->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($suppliers->total()); ?></h3>
                <p>إجمالي الموردين</p>
            </div>
            <div class="icon">
                <i class="fas fa-truck"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($suppliers->where('status', 'active')->count()); ?></h3>
                <p>الموردين النشطين</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($suppliers->where('supplier_type', 'company')->count()); ?></h3>
                <p>الشركات</p>
            </div>
            <div class="icon">
                <i class="fas fa-building"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($suppliers->where('supplier_type', 'international')->count()); ?></h3>
                <p>موردين دوليين</p>
            </div>
            <div class="icon">
                <i class="fas fa-globe"></i>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    console.log('Suppliers page loaded with notification system');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/suppliers/index.blade.php ENDPATH**/ ?>