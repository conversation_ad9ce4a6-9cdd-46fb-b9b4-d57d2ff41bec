<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Web\DashboardController;
use App\Http\Controllers\Web\SalesController;
use App\Http\Controllers\Web\InventoryController;
use App\Http\Controllers\Web\ProductController;
use App\Http\Controllers\Web\AccountingController;
use App\Http\Controllers\Web\CustomerController;
use App\Http\Controllers\Web\CategoryController;
use App\Http\Controllers\Web\SupplierController;
use App\Http\Controllers\Web\AccountController;
use App\Http\Controllers\Web\TransactionController;
use App\Http\Controllers\Web\UserController;
use App\Http\Controllers\Web\RoleController;
use App\Http\Controllers\Web\ReportController;
use App\Http\Controllers\Web\SettingController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Protected routes (require authentication)
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Sales Routes
    Route::resource('sales', SalesController::class);

    // Inventory Routes
    Route::resource('inventory', InventoryController::class);
    Route::resource('products', ProductController::class);
    Route::post('products/{product}/update-stock', [ProductController::class, 'updateStock'])->name('products.update-stock');

    // Accounting Routes
    Route::resource('accounting', AccountingController::class);

    // Customer Routes
    Route::resource('customers', CustomerController::class);

    // Category Routes
    Route::resource('categories', CategoryController::class);

    // Supplier Routes
    Route::resource('suppliers', SupplierController::class);

    // Account Routes
    Route::resource('accounts', AccountController::class);

    // Transaction Routes
    Route::resource('transactions', TransactionController::class);
    Route::post('transactions/{transaction}/reconcile', [TransactionController::class, 'reconcile'])->name('transactions.reconcile');
    Route::post('transactions/{transaction}/unreconcile', [TransactionController::class, 'unreconcile'])->name('transactions.unreconcile');

    // User Management Routes
    Route::resource('users', UserController::class);
    Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Role Management Routes
    Route::resource('roles', RoleController::class);

    // Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/sales', [ReportController::class, 'sales'])->name('sales');
        Route::get('/inventory', [ReportController::class, 'inventory'])->name('inventory');
        Route::get('/financial', [ReportController::class, 'financial'])->name('financial');
        Route::get('/customers', [ReportController::class, 'customers'])->name('customers');
        Route::get('/export-sales', [ReportController::class, 'exportSales'])->name('export-sales');
    });

    // Settings Routes
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingController::class, 'index'])->name('index');
        Route::put('/update', [SettingController::class, 'update'])->name('update');
        Route::get('/reset', [SettingController::class, 'reset'])->name('reset');
        Route::get('/clear-cache', [SettingController::class, 'clearCache'])->name('clear-cache');
        Route::get('/system-info', [SettingController::class, 'systemInfo'])->name('system-info');
        Route::get('/export', [SettingController::class, 'export'])->name('export');
        Route::post('/import', [SettingController::class, 'import'])->name('import');
    });

    // About Route
    Route::get('/about', function () {
        return view('about');
    })->name('about');

    // Profile Routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
