<?php $__env->startSection('title', 'المعاملات المالية'); ?>
<?php $__env->startSection('page-title', 'إدارة المعاملات المالية'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('accounting.index')); ?>">المحاسبة</a></li>
    <li class="breadcrumb-item active">المعاملات</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Filters -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">فلترة المعاملات</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('transactions.index')); ?>">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="type">نوع المعاملة</label>
                                <select class="form-control" id="type" name="type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="income" <?php echo e(request('type') == 'income' ? 'selected' : ''); ?>>دخل</option>
                                    <option value="expense" <?php echo e(request('type') == 'expense' ? 'selected' : ''); ?>>مصروف</option>
                                    <option value="transfer" <?php echo e(request('type') == 'transfer' ? 'selected' : ''); ?>>تحويل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="account_id">الحساب</label>
                                <select class="form-control" id="account_id" name="account_id">
                                    <option value="">جميع الحسابات</option>
                                    <?php $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($account->id); ?>" <?php echo e(request('account_id') == $account->id ? 'selected' : ''); ?>>
                                            <?php echo e($account->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="date_from">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e(request('date_from')); ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="date_to">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e(request('date_to')); ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="<?php echo e(route('transactions.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transactions List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة المعاملات المالية</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('transactions.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> معاملة جديدة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>رقم المرجع</th>
                                <th>التاريخ</th>
                                <th>الحساب</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>التسوية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <code><?php echo e($transaction->reference_number); ?></code>
                                    <?php if($transaction->invoice_number): ?>
                                        <br><small class="text-muted">فاتورة: <?php echo e($transaction->invoice_number); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($transaction->transaction_date->format('Y-m-d')); ?></td>
                                <td>
                                    <strong><?php echo e($transaction->account->name); ?></strong>
                                    <br><small class="text-muted"><?php echo e($transaction->account->code); ?></small>
                                </td>
                                <td>
                                    <?php switch($transaction->type):
                                        case ('income'): ?>
                                            <span class="badge badge-success">دخل</span>
                                            <?php break; ?>
                                        <?php case ('expense'): ?>
                                            <span class="badge badge-danger">مصروف</span>
                                            <?php break; ?>
                                        <?php case ('transfer'): ?>
                                            <span class="badge badge-info">تحويل</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-secondary"><?php echo e($transaction->type); ?></span>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <strong class="<?php echo e($transaction->type == 'income' ? 'text-success' : 'text-danger'); ?>">
                                        <?php echo e($transaction->type == 'income' ? '+' : '-'); ?><?php echo e(number_format($transaction->amount, 2)); ?>

                                    </strong>
                                </td>
                                <td><?php echo e(Str::limit($transaction->description, 30)); ?></td>
                                <td>
                                    <?php switch($transaction->payment_method):
                                        case ('cash'): ?>
                                            <span class="badge badge-success">نقدي</span>
                                            <?php break; ?>
                                        <?php case ('credit_card'): ?>
                                            <span class="badge badge-primary">بطاقة ائتمان</span>
                                            <?php break; ?>
                                        <?php case ('bank_transfer'): ?>
                                            <span class="badge badge-info">تحويل بنكي</span>
                                            <?php break; ?>
                                        <?php case ('check'): ?>
                                            <span class="badge badge-warning">شيك</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-secondary">أخرى</span>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <?php switch($transaction->status):
                                        case ('completed'): ?>
                                            <span class="badge badge-success">مكتملة</span>
                                            <?php break; ?>
                                        <?php case ('pending'): ?>
                                            <span class="badge badge-warning">في الانتظار</span>
                                            <?php break; ?>
                                        <?php case ('cancelled'): ?>
                                            <span class="badge badge-danger">ملغية</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-secondary"><?php echo e($transaction->status); ?></span>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <?php if($transaction->isReconciled()): ?>
                                        <span class="badge badge-success">مسوّاة</span>
                                        <br><small class="text-muted"><?php echo e($transaction->reconciled_at->format('Y-m-d')); ?></small>
                                    <?php else: ?>
                                        <span class="badge badge-warning">غير مسوّاة</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('transactions.show', $transaction->id)); ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('transactions.edit', $transaction->id)); ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if($transaction->isReconciled()): ?>
                                            <button type="button" class="btn btn-secondary btn-sm" title="إلغاء التسوية"
                                                    onclick="confirmAction('<?php echo e(route('transactions.unreconcile', $transaction->id)); ?>', 'إلغاء تسوية المعاملة', 'هل أنت متأكد من إلغاء تسوية هذه المعاملة؟')">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-success btn-sm" title="تسوية"
                                                    onclick="confirmAction('<?php echo e(route('transactions.reconcile', $transaction->id)); ?>', 'تسوية المعاملة', 'هل أنت متأكد من تسوية هذه المعاملة؟')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('<?php echo e(route('transactions.destroy', $transaction->id)); ?>', 'حذف المعاملة', 'هل أنت متأكد من حذف هذه المعاملة؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="10" class="text-center">لا توجد معاملات</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($transactions->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($transactions->appends(request()->query())->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($transactions->where('type', 'income')->where('status', 'completed')->sum('amount')); ?></h3>
                <p>إجمالي الدخل</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-up"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($transactions->where('type', 'expense')->where('status', 'completed')->sum('amount')); ?></h3>
                <p>إجمالي المصروفات</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-down"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($transactions->where('status', 'completed')->count()); ?></h3>
                <p>المعاملات المكتملة</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($transactions->whereNotNull('reconciled_at')->count()); ?></h3>
                <p>المعاملات المسوّاة</p>
            </div>
            <div class="icon">
                <i class="fas fa-balance-scale"></i>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmAction(url, title, text) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تأكيد!',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            
            form.appendChild(csrfToken);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

$(document).ready(function() {
    console.log('Transactions page loaded with notification system');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/transactions/index.blade.php ENDPATH**/ ?>