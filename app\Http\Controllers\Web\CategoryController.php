<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = Category::with(['parent', 'children'])
            ->withCount('products')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(15);

        return view('categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $parentCategories = Category::whereNull('parent_id')
            ->active()
            ->orderBy('name')
            ->get();

        return view('categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:categories,slug',
                'description' => 'nullable|string|max:1000',
                'parent_id' => 'nullable|exists:categories,id',
                'status' => 'required|in:active,inactive',
                'sort_order' => 'nullable|integer|min:0',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $data = $request->all();

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = \Str::slug($data['name']);
            }

            // Handle image upload
            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $imageName = time() . '_' . $image->getClientOriginalName();
                $image->storeAs('public/categories', $imageName);
                $data['image'] = $imageName;
            }

            // Set sort order if not provided
            if (empty($data['sort_order'])) {
                if ($data['parent_id']) {
                    $data['sort_order'] = Category::where('parent_id', $data['parent_id'])->max('sort_order') + 1;
                } else {
                    $data['sort_order'] = Category::whereNull('parent_id')->max('sort_order') + 1;
                }
            }

            // Create category
            $category = Category::create($data);

            // Success notification
            notify_crud('created', 'category');

            return redirect()->route('categories.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('created', 'category', false);
            
            return back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        $category->load(['parent', 'children', 'products' => function($query) {
            $query->latest()->take(10);
        }]);

        $stats = [
            'products_count' => $category->products()->count(),
            'active_products' => $category->products()->where('status', 'active')->count(),
            'children_count' => $category->children()->count(),
            'total_value' => $category->products()->sum('selling_price')
        ];

        return view('categories.show', compact('category', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        $parentCategories = Category::whereNull('parent_id')
            ->where('id', '!=', $category->id)
            ->active()
            ->orderBy('name')
            ->get();

        return view('categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Category $category)
    {
        try {
            // Validation
            $request->validate([
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:categories,slug,' . $category->id,
                'description' => 'nullable|string|max:1000',
                'parent_id' => 'nullable|exists:categories,id',
                'status' => 'required|in:active,inactive',
                'sort_order' => 'nullable|integer|min:0',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $data = $request->all();

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = \Str::slug($data['name']);
            }

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($category->image) {
                    \Storage::delete('public/categories/' . $category->image);
                }

                $image = $request->file('image');
                $imageName = time() . '_' . $image->getClientOriginalName();
                $image->storeAs('public/categories', $imageName);
                $data['image'] = $imageName;
            }

            // Update category
            $category->update($data);

            // Success notification
            notify_crud('updated', 'category');

            return redirect()->route('categories.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('updated', 'category', false);
            
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        try {
            // Check if category has products
            if ($category->products()->count() > 0) {
                notify_error('لا يمكن حذف التصنيف لأنه يحتوي على منتجات', 'خطأ في الحذف');
                return back();
            }

            // Check if category has children
            if ($category->children()->count() > 0) {
                notify_error('لا يمكن حذف التصنيف لأنه يحتوي على تصنيفات فرعية', 'خطأ في الحذف');
                return back();
            }

            // Delete image
            if ($category->image) {
                \Storage::delete('public/categories/' . $category->image);
            }

            $category->delete();

            // Success notification
            notify_crud('deleted', 'category');

            return redirect()->route('categories.index');

        } catch (\Exception $e) {
            // Error notification
            notify_crud('deleted', 'category', false);
            
            return back();
        }
    }

    /**
     * Get categories tree for API.
     */
    public function getTree()
    {
        $categories = Category::with('children.children')
            ->whereNull('parent_id')
            ->active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        return response()->json($categories);
    }

    /**
     * Get flat list of categories for select options.
     */
    public function getFlatList()
    {
        $categories = $this->buildFlatList();
        return response()->json($categories);
    }

    /**
     * Build flat list recursively.
     */
    private function buildFlatList($parentId = null, $prefix = '')
    {
        $categories = collect();
        
        $items = Category::where('parent_id', $parentId)
            ->active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
            
        foreach ($items as $item) {
            $categories->push([
                'id' => $item->id,
                'name' => $prefix . $item->name,
                'slug' => $item->slug
            ]);
            
            $children = $this->buildFlatList($item->id, $prefix . '-- ');
            $categories = $categories->merge($children);
        }
        
        return $categories;
    }
}
