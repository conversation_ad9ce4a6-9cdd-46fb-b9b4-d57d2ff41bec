<?php $__env->startSection('title', 'الحسابات المحاسبية'); ?>
<?php $__env->startSection('page-title', 'إدارة الحسابات المحاسبية'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">لوحة التحكم</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('accounting.index')); ?>">المحاسبة</a></li>
    <li class="breadcrumb-item active">الحسابات</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة الحسابات المحاسبية</h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('accounts.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> حساب جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم الحساب</th>
                                <th>نوع الحساب</th>
                                <th>الرصيد</th>
                                <th>العملة</th>
                                <th>عدد المعاملات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <code><?php echo e($account->code); ?></code>
                                    <?php if($account->is_default): ?>
                                        <span class="badge badge-warning badge-sm">افتراضي</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo e($account->name); ?></strong>
                                    <?php if($account->description): ?>
                                        <br><small class="text-muted"><?php echo e(Str::limit($account->description, 50)); ?></small>
                                    <?php endif; ?>
                                    <?php if($account->isBankAccount()): ?>
                                        <br><small class="text-info">
                                            <i class="fas fa-university"></i> <?php echo e($account->bank_name); ?>

                                            <?php if($account->account_number): ?>
                                                - <?php echo e($account->masked_account_number); ?>

                                            <?php endif; ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php switch($account->type):
                                        case ('asset'): ?>
                                            <span class="badge badge-success">أصول</span>
                                            <?php break; ?>
                                        <?php case ('liability'): ?>
                                            <span class="badge badge-danger">التزامات</span>
                                            <?php break; ?>
                                        <?php case ('equity'): ?>
                                            <span class="badge badge-primary">حقوق ملكية</span>
                                            <?php break; ?>
                                        <?php case ('revenue'): ?>
                                            <span class="badge badge-info">إيرادات</span>
                                            <?php break; ?>
                                        <?php case ('expense'): ?>
                                            <span class="badge badge-warning">مصروفات</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge badge-secondary"><?php echo e($account->type); ?></span>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <strong class="<?php echo e($account->balance >= 0 ? 'text-success' : 'text-danger'); ?>">
                                        <?php echo e(number_format($account->balance, 2)); ?>

                                    </strong>
                                </td>
                                <td>
                                    <span class="badge badge-light"><?php echo e($account->currency); ?></span>
                                </td>
                                <td>
                                    <span class="badge badge-primary"><?php echo e($account->transactions_count); ?></span>
                                </td>
                                <td>
                                    <?php if($account->status == 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('accounts.show', $account->id)); ?>" class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('accounts.edit', $account->id)); ?>" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if(!$account->is_default && $account->transactions_count == 0): ?>
                                        <button type="button" class="btn btn-danger btn-sm" title="حذف" 
                                                onclick="confirmDelete('<?php echo e(route('accounts.destroy', $account->id)); ?>', 'حذف الحساب', 'هل أنت متأكد من حذف هذا الحساب؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="text-center">لا توجد حسابات</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($accounts->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($accounts->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Account Summary Cards -->
<div class="row">
    <div class="col-lg-2 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3><?php echo e($accounts->where('type', 'asset')->count()); ?></h3>
                <p>الأصول</p>
            </div>
            <div class="icon">
                <i class="fas fa-coins"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3><?php echo e($accounts->where('type', 'liability')->count()); ?></h3>
                <p>الالتزامات</p>
            </div>
            <div class="icon">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-primary">
            <div class="inner">
                <h3><?php echo e($accounts->where('type', 'equity')->count()); ?></h3>
                <p>حقوق الملكية</p>
            </div>
            <div class="icon">
                <i class="fas fa-balance-scale"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3><?php echo e($accounts->where('type', 'revenue')->count()); ?></h3>
                <p>الإيرادات</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-up"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3><?php echo e($accounts->where('type', 'expense')->count()); ?></h3>
                <p>المصروفات</p>
            </div>
            <div class="icon">
                <i class="fas fa-arrow-down"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-secondary">
            <div class="inner">
                <h3><?php echo e($accounts->where('status', 'active')->count()); ?></h3>
                <p>النشطة</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    console.log('Accounts page loaded with notification system');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\OmniFlow-ERP2025\resources\views/accounts/index.blade.php ENDPATH**/ ?>