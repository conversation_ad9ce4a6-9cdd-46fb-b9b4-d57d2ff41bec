# 🚀 دليل التثبيت والنشر - OmniFlow ERP 2025

## 📋 نظرة عامة

هذا الدليل يوضح كيفية تثبيت ونشر نظام OmniFlow ERP 2025 في بيئات مختلفة من التطوير إلى الإنتاج.

---

## 🔧 المتطلبات الأساسية

### متطلبات الخادم
```bash
✅ PHP 8.2 أو أحدث
✅ MySQL 8.0+ أو MariaDB 10.3+
✅ Nginx 1.18+ أو Apache 2.4+
✅ Node.js 16+ و NPM
✅ Composer 2.0+
✅ Git
✅ SSL Certificate (للإنتاج)
```

### إضافات PHP المطلوبة
```bash
✅ BCMath PHP Extension
✅ Ctype PHP Extension  
✅ Fileinfo PHP Extension
✅ JSON PHP Extension
✅ Mbstring PHP Extension
✅ OpenSSL PHP Extension
✅ PDO PHP Extension
✅ Tokenizer PHP Extension
✅ XML PHP Extension
✅ GD PHP Extension (للصور)
✅ Zip PHP Extension
```

### التحقق من المتطلبات
```bash
# التحقق من إصدار PHP
php -v

# التحقق من الإضافات
php -m | grep -E "(bcmath|ctype|fileinfo|json|mbstring|openssl|pdo|tokenizer|xml|gd|zip)"

# التحقق من Composer
composer --version

# التحقق من Node.js
node --version
npm --version
```

---

## 🏠 التثبيت المحلي (Development)

### 1. استنساخ المشروع
```bash
# استنساخ من Git
git clone https://github.com/your-repo/omniflow-erp-2025.git
cd omniflow-erp-2025

# أو تحميل الملفات مباشرة
# ثم فك الضغط في مجلد المشروع
```

### 2. تثبيت التبعيات
```bash
# تثبيت حزم PHP
composer install

# تثبيت حزم Node.js
npm install
```

### 3. إعداد البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate

# إنشاء رابط التخزين
php artisan storage:link
```

### 4. تحديث ملف .env
```env
# معلومات التطبيق
APP_NAME="OmniFlow ERP 2025"
APP_ENV=local
APP_KEY=base64:generated_key_here
APP_DEBUG=true
APP_URL=http://localhost:8000

# قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=omniflow_erp2025
DB_USERNAME=your_username
DB_PASSWORD=your_password

# البريد الإلكتروني (اختياري للتطوير)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# إعدادات إضافية
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
```

### 5. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p
CREATE DATABASE omniflow_erp2025 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

# تشغيل الهجرات
php artisan migrate

# إدخال البيانات الأساسية
php artisan db:seed

# إدخال البيانات التجريبية (اختياري)
php artisan db:seed --class=SimpleDemoSeeder
```

### 6. بناء الأصول
```bash
# للتطوير
npm run dev

# أو للإنتاج
npm run build
```

### 7. تشغيل الخادم
```bash
# تشغيل خادم Laravel
php artisan serve

# أو تحديد المنفذ
php artisan serve --port=8080

# للوصول من الشبكة المحلية
php artisan serve --host=0.0.0.0 --port=8000
```

### 8. الوصول للنظام
```
URL: http://localhost:8000
Email: <EMAIL>
Password: password
```

---

## 🌐 النشر على الخادم (Production)

### 1. إعداد الخادم

#### أ. تثبيت المتطلبات على Ubuntu/Debian
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت PHP 8.2
sudo apt install software-properties-common
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-xml php8.2-gd php8.2-mbstring php8.2-zip php8.2-curl php8.2-bcmath

# تثبيت MySQL
sudo apt install mysql-server
sudo mysql_secure_installation

# تثبيت Nginx
sudo apt install nginx

# تثبيت Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs

# تثبيت Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### ب. تثبيت المتطلبات على CentOS/RHEL
```bash
# تحديث النظام
sudo yum update -y

# تثبيت EPEL و Remi repositories
sudo yum install epel-release
sudo yum install https://rpms.remirepo.net/enterprise/remi-release-8.rpm

# تثبيت PHP 8.2
sudo yum module enable php:remi-8.2
sudo yum install php php-fpm php-mysql php-xml php-gd php-mbstring php-zip php-curl php-bcmath

# تثبيت MySQL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
sudo mysql_secure_installation

# تثبيت Nginx
sudo yum install nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. إعداد قاعدة البيانات
```bash
# الدخول إلى MySQL
sudo mysql -u root -p

# إنشاء قاعدة البيانات والمستخدم
CREATE DATABASE omniflow_erp2025 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'omniflow_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON omniflow_erp2025.* TO 'omniflow_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. رفع ملفات المشروع
```bash
# إنشاء مجلد المشروع
sudo mkdir -p /var/www/omniflow-erp
sudo chown -R $USER:$USER /var/www/omniflow-erp

# رفع الملفات (عبر Git أو FTP)
cd /var/www/omniflow-erp
git clone https://github.com/your-repo/omniflow-erp-2025.git .

# أو رفع الملفات عبر SCP/FTP
# scp -r ./omniflow-erp-2025/* user@server:/var/www/omniflow-erp/
```

### 4. تثبيت التبعيات
```bash
cd /var/www/omniflow-erp

# تثبيت حزم PHP (بدون dev dependencies)
composer install --optimize-autoloader --no-dev

# تثبيت حزم Node.js
npm ci --only=production

# بناء الأصول للإنتاج
npm run build
```

### 5. إعداد البيئة للإنتاج
```bash
# نسخ وتحديث ملف البيئة
cp .env.example .env
nano .env
```

```env
# ملف .env للإنتاج
APP_NAME="OmniFlow ERP 2025"
APP_ENV=production
APP_KEY=base64:your_generated_key_here
APP_DEBUG=false
APP_URL=https://your-domain.com

# قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=omniflow_erp2025
DB_USERNAME=omniflow_user
DB_PASSWORD=strong_password_here

# Cache & Sessions
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_CONNECTION=database

# البريد الإلكتروني
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-server.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# الأمان
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=your-domain.com
```

### 6. إعداد الصلاحيات
```bash
# تعيين المالك
sudo chown -R www-data:www-data /var/www/omniflow-erp

# تعيين صلاحيات المجلدات
sudo find /var/www/omniflow-erp -type d -exec chmod 755 {} \;

# تعيين صلاحيات الملفات
sudo find /var/www/omniflow-erp -type f -exec chmod 644 {} \;

# صلاحيات خاصة للمجلدات الحساسة
sudo chmod -R 775 /var/www/omniflow-erp/storage
sudo chmod -R 775 /var/www/omniflow-erp/bootstrap/cache
```

### 7. تشغيل الهجرات
```bash
cd /var/www/omniflow-erp

# توليد مفتاح التطبيق
php artisan key:generate

# إنشاء رابط التخزين
php artisan storage:link

# تشغيل الهجرات
php artisan migrate --force

# إدخال البيانات الأساسية
php artisan db:seed --force

# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 8. إعداد Nginx
```bash
# إنشاء ملف التكوين
sudo nano /etc/nginx/sites-available/omniflow-erp
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/omniflow-erp/public;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/omniflow-erp /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 9. إعداد SSL (Let's Encrypt)
```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx

# الحصول على شهادة SSL
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# تجديد تلقائي
sudo crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
```

### 10. إعداد المراقبة والنسخ الاحتياطي

#### أ. إعداد النسخ الاحتياطي
```bash
# إنشاء سكريبت النسخ الاحتياطي
sudo nano /usr/local/bin/backup-omniflow.sh
```

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/omniflow"
DB_NAME="omniflow_erp2025"
DB_USER="omniflow_user"
DB_PASS="strong_password_here"
APP_DIR="/var/www/omniflow-erp"

# إنشاء مجلد النسخ الاحتياطي
mkdir -p $BACKUP_DIR

# نسخ احتياطي لقاعدة البيانات
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/database_$DATE.sql

# نسخ احتياطي للملفات
tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C $APP_DIR storage public/uploads

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

```bash
# تعيين صلاحيات التنفيذ
sudo chmod +x /usr/local/bin/backup-omniflow.sh

# إضافة مهمة مجدولة (يومياً في 2:00 صباحاً)
sudo crontab -e
0 2 * * * /usr/local/bin/backup-omniflow.sh
```

#### ب. إعداد مراقبة السجلات
```bash
# إعداد logrotate
sudo nano /etc/logrotate.d/omniflow-erp
```

```
/var/www/omniflow-erp/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

---

## 🔧 التحسين والأداء

### 1. تحسين PHP
```bash
# تحديث php.ini
sudo nano /etc/php/8.2/fpm/php.ini
```

```ini
# تحسينات الأداء
memory_limit = 256M
max_execution_time = 300
max_input_vars = 3000
upload_max_filesize = 20M
post_max_size = 20M

# تحسينات OPcache
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

### 2. تحسين MySQL
```bash
# تحديث my.cnf
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
# تحسينات الأداء
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 32M
```

### 3. تحسين Laravel
```bash
cd /var/www/omniflow-erp

# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# تحسين Composer
composer dump-autoload --optimize
```

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ 500 Internal Server Error
```bash
# فحص سجلات الأخطاء
tail -f /var/www/omniflow-erp/storage/logs/laravel.log
tail -f /var/log/nginx/error.log

# التحقق من الصلاحيات
sudo chown -R www-data:www-data /var/www/omniflow-erp/storage
sudo chmod -R 775 /var/www/omniflow-erp/storage
```

#### 2. مشاكل قاعدة البيانات
```bash
# التحقق من الاتصال
php artisan tinker
DB::connection()->getPdo();

# إعادة تشغيل الهجرات
php artisan migrate:fresh --seed
```

#### 3. مشاكل الصلاحيات
```bash
# إعادة تعيين الصلاحيات
sudo chown -R www-data:www-data /var/www/omniflow-erp
sudo find /var/www/omniflow-erp -type d -exec chmod 755 {} \;
sudo find /var/www/omniflow-erp -type f -exec chmod 644 {} \;
sudo chmod -R 775 /var/www/omniflow-erp/storage
sudo chmod -R 775 /var/www/omniflow-erp/bootstrap/cache
```

#### 4. مشاكل الذاكرة
```bash
# زيادة حد الذاكرة
echo "memory_limit = 512M" | sudo tee -a /etc/php/8.2/fpm/php.ini
sudo systemctl restart php8.2-fpm
```

---

## ✅ قائمة التحقق النهائية

### قبل النشر
- [ ] تم تحديث جميع التبعيات
- [ ] تم اختبار جميع الوظائف
- [ ] تم إعداد ملف .env للإنتاج
- [ ] تم تعطيل وضع التطوير (APP_DEBUG=false)
- [ ] تم إعداد النسخ الاحتياطي
- [ ] تم إعداد شهادة SSL
- [ ] تم اختبار الأداء

### بعد النشر
- [ ] تم التحقق من عمل جميع الصفحات
- [ ] تم اختبار تسجيل الدخول
- [ ] تم التحقق من عمل API
- [ ] تم اختبار النسخ الاحتياطي
- [ ] تم إعداد المراقبة
- [ ] تم توثيق معلومات الخادم

---

## 🎉 تهانينا!

تم تثبيت ونشر OmniFlow ERP 2025 بنجاح! 

النظام جاهز للاستخدام على: **https://your-domain.com**

**بيانات الدخول الافتراضية:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password`

**لا تنس تغيير كلمة المرور الافتراضية فور تسجيل الدخول!**
